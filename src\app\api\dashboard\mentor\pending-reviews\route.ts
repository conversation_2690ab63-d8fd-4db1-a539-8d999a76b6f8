import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import { isMediaPath, safeMediaQuery } from '@/lib/media-utils'
import payloadConfig from '@/payload.config'

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 })
    }

    const user = await extractUserFromToken(token)

    if (!user || user.role !== 'mentor') {
      return NextResponse.json(
        { error: 'Unauthorized: Only mentors can access this endpoint' },
        { status: 403 },
      )
    }

    // Get the school ID from the user
    const schoolId = user.school

    if (!schoolId) {
      return NextResponse.json(
        { error: 'You must be associated with a school to access this endpoint' },
        { status: 400 },
      )
    }

    // Get articles with teacher reviews that haven't been rated by a mentor
    const payload = await getPayload({ config: payloadConfig })
    
    try {
      // First, simply get all articles from this school
      const articles = await payload.find({
        collection: 'articles',
        where: {
          // Use a simple query with proper equals operator
          'author.school': { equals: schoolId }
        },
        depth: 2, // To get teacher and article details
      });
      
      // Filter in JavaScript instead of MongoDB query to avoid ObjectParameterError
      const validArticles = articles.docs
        // Remove articles with media path IDs
        .filter(article => article && article.id && !isMediaPath(article.id))
        // Only include articles with teacher reviews
        .filter(article => article && article.teacherReview && Array.isArray(article.teacherReview) && article.teacherReview.length > 0);
      
      // Extract teacher reviews that haven't been rated by a mentor
      const pendingReviews = [];
      
      for (const article of validArticles) {
        if (article.teacherReview && article.teacherReview.length > 0) {
          for (let index = 0; index < article.teacherReview.length; index++) {
            const review = article.teacherReview[index];
            
            // Skip if review has mentor ratings
            if (review.mentorReview && Array.isArray(review.mentorReview) && review.mentorReview.length > 0) {
              continue;
            }
            
            // Get teacher name
            let teacherName = 'Unknown Teacher';
            if (typeof review.reviewer === 'object' && review.reviewer) {
              const firstName = review.reviewer.firstName || '';
              const lastName = review.reviewer.lastName || '';
              const email = review.reviewer.email || '';
              teacherName = `${firstName} ${lastName}`.trim() || email;
            }
            
            pendingReviews.push({
              id: `${article.id}-${index}`,
              articleId: article.id,
              articleTitle: article.title || 'Untitled Article',
              teacherId: typeof review.reviewer === 'object' ? review.reviewer.id : review.reviewer,
              teacherName,
              rating: review.rating || 0,
              comment: review.comment || '',
              approved: review.approved || false,
              createdAt: review.createdAt || article.createdAt,
              reviewIndex: index,
            });
          }
        }
      }
      
      return NextResponse.json(
        {
          success: true,
          reviews: pendingReviews,
        },
        { status: 200 },
      );
    } catch (error) {
      console.error('Error fetching pending reviews:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch pending reviews',
        details: error instanceof Error ? error.message : String(error)
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error fetching pending reviews:', error)
    return NextResponse.json({ error: 'Failed to fetch pending reviews' }, { status: 500 })
  }
}
