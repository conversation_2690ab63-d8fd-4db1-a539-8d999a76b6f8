'use server'

import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import jwt from 'jsonwebtoken'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const payload = await getPayload({ config })

    // Verify the token
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

    // Get the user ID from the token
    const userId = typeof decoded === 'object' ? decoded.id : null

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 2,
    })

    // Verify user role - handle both string ID and populated object cases
    let roleSlug: string
    if (typeof user.role === 'string') {
      const role = await payload.findByID({
        collection: 'roles',
        id: user.role,
        depth: 0,
      })
      roleSlug = role?.slug || ''
    } else if (user.role && typeof user.role === 'object') {
      roleSlug = user.role.slug || ''
    } else {
      return NextResponse.json({ error: 'Invalid user role configuration' }, { status: 400 })
    }

    // Check allowed roles
    if (!['mentor', 'super-admin', 'school-admin'].includes(roleSlug)) {
      return NextResponse.json(
        { error: 'Forbidden', message: 'Only mentors and admins can access this endpoint' },
        { status: 403 },
      )
    }

    // Get school ID - handle both string ID and populated object cases
    let schoolId: string | undefined
    if (typeof user.school === 'string') {
      schoolId = user.school
    } else if (user.school && typeof user.school === 'object') {
      schoolId = user.school.id
    }

    // Only require school for non-super-admins
    if (roleSlug !== 'super-admin' && !schoolId) {
      return NextResponse.json(
        { error: 'Mentor must be associated with a school' },
        { status: 400 },
      )
    }

    // Get URL parameters
    const url = new URL(req.url)
    const querySchoolId = url.searchParams.get('schoolId')
    
    // Use the query schoolId if provided, otherwise use the user's schoolId
    const targetSchoolId = querySchoolId || schoolId

    // Get teachers in the school
    const teachers = await payload.find({
      collection: 'users',
      where: {
        role: { equals: 'teacher' },
        school: { equals: targetSchoolId },
      },
    })

    // For each teacher, get their review statistics
    const teacherStats = await Promise.all(
      teachers.docs.map(async (teacher) => {
        // Get all articles reviewed by this teacher
        const articles = await payload.find({
          collection: 'articles',
          where: {
            'teacherReview.reviewer': {
              equals: teacher.id,
            },
          },
        })

        let totalReviews = 0
        let approvedReviews = 0
        let totalRating = 0
        let reviewsThisMonth = 0

        // Calculate statistics
        articles.docs.forEach((article) => {
          if (article.teacherReview) {
            article.teacherReview.forEach((review: any) => {
              if (review.reviewer === teacher.id) {
                totalReviews++
                if (review.approved) {
                  approvedReviews++
                }
                if (review.rating) {
                  totalRating += review.rating
                }

                // Check if review was done this month
                const reviewDate = new Date(review.createdAt || article.updatedAt)
                const now = new Date()
                if (
                  reviewDate.getMonth() === now.getMonth() &&
                  reviewDate.getFullYear() === now.getFullYear()
                ) {
                  reviewsThisMonth++
                }
              }
            })
          }
        })

        // Calculate approval rate and average rating
        const approvalRate = totalReviews > 0 ? approvedReviews / totalReviews : 0
        const averageRating = totalReviews > 0 ? totalRating / totalReviews : 0

        // Return anonymized teacher data
        return {
          teacherId: teacher.id,
          teacherName: `Teacher_${teacher.id.substring(0, 6)}`,
          totalReviews,
          approvalRate,
          averageRating,
          reviewsThisMonth,
        }
      }),
    )

    return NextResponse.json({ teachers: teacherStats })
  } catch (error) {
    console.error('Error fetching teacher review analytics:', error)
    return NextResponse.json({ error: 'Failed to fetch teacher review analytics' }, { status: 500 })
  }
}
