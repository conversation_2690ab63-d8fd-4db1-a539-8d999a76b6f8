'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

interface Activity {
  date: string
  count: number
  schoolId?: string
  schoolName?: string
}

export function GlobalActivityHeatmap() {
  const [loading, setLoading] = useState(true)
  const [activities, setActivities] = useState<Activity[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/dashboard/super-admin/activities?global=true')
        
        if (!response.ok) {
          throw new Error('فشل في جلب الأنشطة')
        }

        const data = await response.json()
        setActivities(data.activities || data || [])
        setLoading(false)
      } catch (error) {
        console.error('Error fetching global activities:', error)
        setError('فشل في تحميل بيانات النشاط')
        setLoading(false)
      }
    }

    fetchActivities()
  }, [])

  // Group activities by date
  const groupedActivities = activities.reduce((acc, activity) => {
    const date = new Date(activity.date)
    const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    
    if (!acc[dateKey]) {
      acc[dateKey] = {
        date: dateKey,
        count: 0,
        schools: {}
      }
    }
    
    acc[dateKey].count += activity.count
    
    if (activity.schoolId) {
      if (!acc[dateKey].schools[activity.schoolId]) {
        acc[dateKey].schools[activity.schoolId] = {
          count: 0,
          name: activity.schoolName || activity.schoolId
        }
      }
      acc[dateKey].schools[activity.schoolId].count += activity.count
    }
    
    return acc
  }, {} as Record<string, any>)

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-[200px] w-full" />
      </div>
    )
  }

  if (error) {
    return <div className="text-red-500">خطأ: {error}</div>
  }

  // For demonstration purposes - since we don't have the API endpoint yet
  // This will be replaced with actual data from the API
  const demoData = [
    { date: '2023-05-01', count: 5 },
    { date: '2023-05-02', count: 12 },
    { date: '2023-05-03', count: 8 },
    { date: '2023-05-04', count: 15 },
    { date: '2023-05-05', count: 25 },
    { date: '2023-05-06', count: 18 },
    { date: '2023-05-07', count: 9 },
  ]

  return (
    <div className="space-y-4" dir="rtl">
      <div className="text-sm text-muted-foreground">
        تظهر هذه الخريطة الحرارية الأنشطة عبر جميع المدارس في النظام. يشير اللون الأعمق إلى حجم نشاط أعلى.
      </div>
      
      <div className="grid grid-cols-7 gap-1 py-4">
        {/* Mock heatmap visualization - will be replaced with a proper visualization component */}
        {(Object.values(groupedActivities).length > 0 ? Object.values(groupedActivities) : demoData).map((day, i) => {
          // Calculate intensity - 0 to 1 scale based on activity count
          const maxCount = Math.max(...demoData.map(d => d.count))
          const intensity = Math.min(1, day.count / maxCount)
          const color = `rgba(0, 112, 243, ${Math.max(0.1, intensity)})`
          
          return (
            <div key={i} className="relative group">
              <div 
                className="h-10 rounded-sm cursor-pointer"
                style={{ backgroundColor: color }}
                title={`${day.date}: ${day.count} نشاط`}
              />
              
              <div className="absolute hidden group-hover:block z-10 p-2 bg-background border rounded shadow-md text-xs whitespace-nowrap left-1/2 -translate-x-1/2 -translate-y-full -top-1">
                <div className="font-medium">{day.date}</div>
                <div>{day.count} نشاط</div>
              </div>
            </div>
          )
        })}
      </div>
      
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>أقل</span>
        <div className="flex space-x-1">
          <div className="w-4 h-4 bg-blue-500/10 rounded-sm"></div>
          <div className="w-4 h-4 bg-blue-500/30 rounded-sm"></div>
          <div className="w-4 h-4 bg-blue-500/50 rounded-sm"></div>
          <div className="w-4 h-4 bg-blue-500/70 rounded-sm"></div>
          <div className="w-4 h-4 bg-blue-500/90 rounded-sm"></div>
        </div>
        <span>أكثر</span>
      </div>
    </div>
  )
} 