import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { ClientHeroCarousel } from '@/components/ClientHeroCarousel'
import { ClientDeconstructedCardCarousel } from '@/components/DeconstructedCardCarousel'
import { DeconstructedCard } from '@/components/DeconstructedCard'
import { FullscreenSectionsContainer } from '@/components/FullscreenSectionsContainer'
import bgImage from '../../public/media/ondrej-bocek-RcHgAfpwWXo-unsplash.jpg'
import bgImage2 from '../../public/media/ondrej-bocek-RcHgAfpwWXo-unsplash-2.jpg'
import bgImage3 from '../../public/media/ondrej-bocek-RcHgAfpwWXo-unsplash-3.jpg'
import { TopLeaders } from '@/components/TopLeaders'
import { Button } from '@/components/ui/button'
import { SharedLayout } from '@/components/SharedLayout'
import { isMediaPath, safeMediaQuery, safePayloadResponse } from '@/lib/media-utils'
import { connectToDatabase } from '@/lib/mongodb'
import { getImageUrl, getImageAlt } from '@/utils/imageUtils'

export default async function HomePage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers })

  // Determine the admin route based on user role
  let adminRoute = '/admin'
  if (user) {
    try {
      const userWithRole = await payload.findByID({
        collection: 'users',
        id: user.id,
        depth: 1,
      })

      if (userWithRole && userWithRole.role) {
        const roleSlug = typeof userWithRole.role === 'object' ? userWithRole.role.slug : null

        if (roleSlug === 'super-admin') {
          adminRoute = '/admin'
        } else if (roleSlug === 'school-admin') {
          adminRoute = '/admin/collections/users'
        } else if (roleSlug === 'mentor') {
          adminRoute = '/admin/collections/news'
        } else if (roleSlug === 'teacher') {
          adminRoute = '/admin/collections/articles'
        } else if (roleSlug === 'student') {
          adminRoute = '/admin/collections/articles'
        }
      }
    } catch (error) {
      console.error('Error fetching user role:', error)
    }
  }

  // Fetch latest news
  // Apply safe media query to prevent ObjectId casting errors
  const newsQuery = safeMediaQuery({
    status: {
      equals: 'published',
    },
  })

  let latestNews

  try {
    // Try to fetch news with Payload
    const newsResponse = await payload.find({
      collection: 'news',
      where: newsQuery,
      sort: '-publishedAt',
      limit: 8,
      depth: 1, // Populate author
    })

    // Process the response to filter out any media paths
    latestNews = safePayloadResponse(newsResponse)
  } catch (error) {
    console.error('Error fetching news with Payload:', error)

    // Fallback to MongoDB if there's an error
    try {
      console.log('Falling back to direct MongoDB query for home page news')
      const { db } = await connectToDatabase()
      const mongoNews = await db
        .collection('news')
        .find({ status: 'published' })
        .sort({ publishedAt: -1 })
        .limit(8)
        .toArray()

      console.log(`Found ${mongoNews.length} news items with MongoDB query`)

      // Format the response to match Payload's format
      latestNews = {
        docs: mongoNews,
        totalDocs: mongoNews.length,
        limit: 8,
        totalPages: 1,
        page: 1,
        pagingCounter: 1,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null,
      }
    } catch (mongoError) {
      console.error('Error with MongoDB fallback for news:', mongoError)
      // Return empty results instead of throwing an error
      latestNews = {
        docs: [],
        totalDocs: 0,
        limit: 8,
        totalPages: 0,
        page: 1,
        pagingCounter: 1,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null,
      }
    }
  }

  // Fetch latest student articles
  // Apply safe media query to prevent ObjectId casting errors
  const articlesQuery = safeMediaQuery({
    status: {
      equals: 'published',
    },
  })

  let latestArticles = {
    docs: [],
    totalDocs: 0,
    limit: 8,
    totalPages: 0,
    page: 1,
    pagingCounter: 1,
    hasPrevPage: false,
    hasNextPage: false,
    prevPage: null,
    nextPage: null,
  }

  // Skip Payload CMS for articles since we know it has issues with media paths
  // Go directly to MongoDB
  try {
    console.log('Using direct MongoDB query for home page articles')
    const { db } = await connectToDatabase()
    const mongoArticles = await db
      .collection('articles')
      .find({ status: 'published' })
      .sort({ createdAt: -1 })
      .limit(8)
      .toArray()

    console.log(`Found ${mongoArticles.length} articles with MongoDB query`)

    // Format the response to match Payload's format
    latestArticles = {
      docs: mongoArticles,
      totalDocs: mongoArticles.length,
      limit: 8,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    }
  } catch (mongoError) {
    console.error('Error with MongoDB fallback:', mongoError)
    // Return empty results instead of throwing an error
    latestArticles = {
      docs: [],
      totalDocs: 0,
      limit: 8,
      totalPages: 0,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    }
  }

  // Fetch top students for sidebar
  const studentRankings = await payload.find({
    collection: 'statistics',
    where: {
      type: {
        equals: 'studentRanking',
      },
    },
    limit: 1,
  })

  // Extract student data
  const studentData = studentRankings.docs.length > 0 ? studentRankings.docs[0].data : []

  // Format for TopLeaders component
  const topStudents = Array.isArray(studentData)
    ? studentData.slice(0, 5).map((student: any) => ({
        id: student.id,
        name: student.name,
        score: student.averageRating || student.articleCount || 0,
        type: 'student' as const,
      }))
    : []

  // Check if user is a student for conditional rendering
  const isStudent =
    user &&
    (typeof user.role === 'object' ? user.role?.slug === 'student' : user.role === 'student')

  // Hero carousel slides
  const heroSlides = [
    {
      title: 'الصحفي الصغير',
      description: 'تمكين الصحفيين الطلاب من مشاركة صوتهم مع العالم',
      imageUrl: bgImage.src,
      buttonText: 'اقرأ المقالات',
      buttonLink: '/articles',
      secondaryButtonText: isStudent ? 'اكتب مقالاً' : undefined,
      secondaryButtonLink: isStudent ? '/articles/create' : undefined,
    },
    {
      title: 'طور مهاراتك في الكتابة',
      description: 'احصل على تعليقات من المعلمين والموجهين لتحسين صحافتك',
      imageUrl: bgImage2.src,
      buttonText: isStudent ? 'ابدأ الكتابة' : 'اعرف المزيد',
      buttonLink: isStudent ? '/articles/create' : '/about',
    },
    {
      title: 'ابق على اطلاع',
      description: 'اقرأ آخر الأخبار والمقالات من الصحفيين الطلاب',
      imageUrl: bgImage3.src,
      buttonText: 'عرض الأخبار',
      buttonLink: '/news',
    },
  ]

  return (
    <SharedLayout user={user} adminRoute={adminRoute}>
      {/* Hero Carousel */}
      <ClientHeroCarousel slides={heroSlides} />

      {/* Main Content Section */}
      <div className="py-16 px-4" id="main-content">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-12">
              {/* Combined Articles and News Section */}
              <div id="content-sections">
                {/* Latest Articles Section */}
                <div id="latest-articles" className="mb-16">
                  <ClientDeconstructedCardCarousel
                    title="أحدث المقالات"
                    viewAllLink="/articles"
                    itemsPerPage={4}
                  >
                    {latestArticles.docs.map((article: any) => {
                      // Skip articles with media path IDs
                      if (article.id && typeof article.id === 'string' && isMediaPath(article.id)) {
                        console.log('Skipping article with media path ID:', article.id)
                        return null
                      }

                      // Generate a safe ID for articles without a valid ID
                      let safeId = article.id

                      // If the ID is missing or invalid, generate one from the title
                      if (!safeId || typeof safeId !== 'string' || safeId.trim() === '') {
                        safeId = article.title
                          ? article.title
                              .toLowerCase()
                              .replace(/[^a-z0-9]+/g, '-')
                              .replace(/(^-|-$)/g, '')
                          : 'article-' + Math.random().toString(36).substring(2, 10)
                        console.log('Generated ID for article without valid ID:', safeId)
                      }

                      // Get author name
                      const authorName =
                        typeof article.author === 'object'
                          ? article.author.firstName && article.author.lastName
                            ? `${article.author.firstName} ${article.author.lastName}`
                            : 'Anonymous Author'
                          : 'Unknown Author'

                      // Log the raw featuredImage for debugging
                      console.log('Raw featuredImage:', article.featuredImage)

                      // Handle different types of featuredImage
                      let imageToUse = article.featuredImage

                      // If it's a media path string, use it directly
                      if (
                        article.featuredImage &&
                        typeof article.featuredImage === 'string' &&
                        isMediaPath(article.featuredImage)
                      ) {
                        console.log(
                          'Article has media path as featuredImage, using it directly:',
                          article.featuredImage,
                        )
                        imageToUse = article.featuredImage
                      }

                      // If it's a MongoDB ID string, try to construct a media URL
                      if (
                        article.featuredImage &&
                        typeof article.featuredImage === 'string' &&
                        /^[0-9a-fA-F]{24}$/.test(article.featuredImage) &&
                        !isMediaPath(article.featuredImage)
                      ) {
                        console.log('Article has MongoDB ID as featuredImage, constructing URL')
                        imageToUse = { url: `/api/media/${article.featuredImage}` }
                      }

                      // Get featured image using the utility function
                      const featuredImage = getImageUrl(
                        imageToUse, // Pass the processed featuredImage - getImageUrl will handle all cases
                        'https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80',
                      )

                      // Format date
                      const formattedDate = article.publishedAt
                        ? new Date(article.publishedAt).toLocaleDateString()
                        : article.createdAt
                          ? new Date(article.createdAt).toLocaleDateString()
                          : 'No date'

                      return (
                        <DeconstructedCard
                          key={safeId}
                          id={safeId}
                          title={article.title || 'Untitled Article'}
                          category="ARTICLE"
                          date={formattedDate}
                          imageUrl={featuredImage}
                          type="article"
                        />
                      )
                    })}
                  </ClientDeconstructedCardCarousel>
                </div>

                {/* Latest News Section */}
                <div id="latest-news">
                  <ClientDeconstructedCardCarousel
                    title="أحدث الأخبار"
                    viewAllLink="/news"
                    itemsPerPage={4}
                  >
                    {latestNews.docs.map((news: any) => {
                      // Skip news with media path IDs
                      if (news.id && typeof news.id === 'string' && isMediaPath(news.id)) {
                        console.log('Skipping news with media path ID:', news.id)
                        return null
                      }

                      // Generate a safe ID for news without a valid ID
                      let safeId = news.slug || news.id

                      // If the ID is missing or invalid, generate one from the title
                      if (
                        !safeId ||
                        typeof safeId !== 'string' ||
                        safeId.trim() === '' ||
                        isMediaPath(safeId)
                      ) {
                        safeId = news.title
                          ? news.title
                              .toLowerCase()
                              .replace(/[^a-z0-9]+/g, '-')
                              .replace(/(^-|-$)/g, '')
                          : 'news-' + Math.random().toString(36).substring(2, 10)
                        console.log('Generated ID for news without valid ID:', safeId)
                      }

                      // Log the raw image for debugging
                      console.log('Raw news image:', news.featuredImage)

                      // Handle different types of image
                      let imageToUse = news.featuredImage

                      // If it's a media path string, use it directly
                      if (
                        news.featuredImage &&
                        typeof news.featuredImage === 'string' &&
                        isMediaPath(news.featuredImage)
                      ) {
                        console.log(
                          'News has media path as image, using it directly:',
                          news.featuredImage,
                        )
                        imageToUse = news.featuredImage
                      }

                      // If it's a MongoDB ID string, try to construct a media URL
                      if (
                        news.featuredImage &&
                        typeof news.featuredImage === 'string' &&
                        /^[0-9a-fA-F]{24}$/.test(news.featuredImage) &&
                        !isMediaPath(news.featuredImage)
                      ) {
                        console.log('News has MongoDB ID as image, constructing URL')
                        imageToUse = { url: `/api/media/${news.featuredImage}` }
                      }

                      // Get image using the utility function
                      const newsImage = getImageUrl(
                        imageToUse, // Pass the processed image - getImageUrl will handle all cases
                        'https://images.unsplash.com/photo-1585829365295-ab7cd400c167?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80',
                      )

                      // Format date
                      const formattedDate = news.publishedAt
                        ? new Date(news.publishedAt).toLocaleDateString()
                        : news.createdAt
                          ? new Date(news.createdAt).toLocaleDateString()
                          : 'No date'

                      // Extract text from rich text object for snippet
                      let contentSnippet = 'اقرأ المزيد...' // Default snippet
                      let extractedText = ''
                      const maxSnippetLength = 150

                      if (
                        news.content &&
                        typeof news.content === 'object' &&
                        'root' in news.content &&
                        news.content.root.children &&
                        Array.isArray(news.content.root.children)
                      ) {
                        // Traverse children nodes to find text
                        for (const child of news.content.root.children) {
                          if (extractedText.length >= maxSnippetLength) break // Stop if we have enough text

                          // If it's a text node, append its text
                          // {{consider-lint}} Potential 'any' usage here
                          if (
                            (child as any).type === 'text' &&
                            typeof (child as any).text === 'string'
                          ) {
                            extractedText += (child as any).text
                          }
                          // If it's a node with children (like paragraph, heading, list item), traverse its children
                          else if (
                            (child as any).children &&
                            Array.isArray((child as any).children)
                          ) {
                            for (const grandChild of (child as any).children) {
                              if (extractedText.length >= maxSnippetLength) break

                              // {{consider-lint}} Potential 'any' usage here
                              if (
                                (grandChild as any).type === 'text' &&
                                typeof (grandChild as any).text === 'string'
                              ) {
                                extractedText += (grandChild as any).text
                              }
                              // Add more cases here if there are nested structures beyond text
                            }
                          }
                          // Add more cases here for other top-level node types if they can contain text directly
                        }

                        // Trim and truncate the extracted text
                        extractedText = extractedText.trim()
                        if (extractedText.length > maxSnippetLength) {
                          contentSnippet = extractedText.substring(0, maxSnippetLength) + '...'
                        } else if (extractedText.length > 0) {
                          contentSnippet = extractedText
                        }
                      }

                      return (
                        <DeconstructedCard
                          key={safeId}
                          id={safeId}
                          title={news.title || 'Untitled News'}
                          category="NEWS"
                          date={formattedDate}
                          imageUrl={newsImage}
                          type="news"
                          color="#0c4a6e"
                        />
                      )
                    })}
                  </ClientDeconstructedCardCarousel>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:sticky lg:top-24 lg:self-start">
              <div
                id="sticky-sidebar"
                className="flex flex-col items-center justify-center max-w-xs mx-auto"
              >
                {/* Top Leaders Widget */}
                <div className="w-full mb-8">
                  <TopLeaders leaders={topStudents} title="أفضل الكتاب الطلاب" />
                </div>

                {/* Call to Action */}
                <div className="w-full bg-primary text-primary-foreground  shadow-md p-6 text-center">
                  <h3 className="text-xl font-bold mb-4">انضم إلى مجتمعنا</h3>
                  <p className="mb-6">
                    كن مراسلاً شاباً وشارك صوتك مع العالم. احصل على تعليقات من المعلمين والموجهين.
                  </p>
                  <Button asChild variant="secondary" className="w-full">
                    <Link href="/register">سجل الآن</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Fullscreen Sections */}
      <FullscreenSectionsContainer />
    </SharedLayout>
  )
}
