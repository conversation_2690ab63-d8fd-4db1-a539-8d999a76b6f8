'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface School {
  id: string
  name: string
}

interface ModernRegisterFormProps {
  schools: School[]
}

export const ModernRegisterForm: React.FC<ModernRegisterFormProps> = ({ schools }) => {
  const [email, setEmail] = useState('')
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [school, setSchool] = useState('')
  const [grade, setGrade] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    
    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      return
    }
    
    setIsLoading(true)

    try {
      // Submit using JSON format
      const response = await fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          firstName,
          lastName,
          password,
          confirmPassword,
          school,
          grade: grade || undefined,
        }),
      })

      if (response.redirected) {
        // Follow the redirect
        window.location.href = response.url
        return
      }

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed')
      }

      // Redirect to pending approval page
      window.location.href = '/pending-approval'
    } catch (err) {
      console.error('Registration error:', err)
      setError(err instanceof Error ? err.message : 'Registration failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <section className="flex flex-col md:flex-row h-screen items-center">
      {/* Left side - Image */}
      <div className="bg-primary hidden lg:block w-full md:w-1/2 xl:w-1/2 h-screen relative">
        <Image 
          src="https://images.unsplash.com/photo-1519452575417-564c1401ecc0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1441&q=100" 
          alt="Young Reporter" 
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-primary/30"></div>
        <div className="absolute bottom-0 left-0 right-0 p-12 text-white">
          <h2 className="text-4xl font-bold mb-4">Join Young Reporter</h2>
          <p className="text-xl">Become part of our community and start sharing your stories today.</p>
        </div>
      </div>

      {/* Right side - Register Form */}
      <div className="bg-background w-full md:max-w-md lg:max-w-full md:mx-auto md:w-1/2 xl:w-1/2 h-screen px-6 lg:px-16 xl:px-12
            flex items-center justify-center overflow-y-auto">
        <div className="w-full py-8">

          <h1 className="text-xl md:text-2xl font-bold leading-tight mt-8">Create your account</h1>

          {error && (
            <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded mt-4 mb-4">
              {error}
            </div>
          )}

          <form className="mt-6 grid grid-cols-2 gap-4" onSubmit={handleSubmit}>
           
            <div className="mb-4">
              <Label htmlFor="firstName" className="block text-foreground">First Name</Label>
              <Input 
                type="text" 
                id="firstName"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder="First Name" 
                className="w-full mt-2" 
                required 
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="lastName" className="block text-foreground">Last Name</Label>
              <Input 
                type="text" 
                id="lastName"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                placeholder="Last Name" 
                className="w-full mt-2" 
                required 
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="email" className="block text-foreground">Email Address</Label>
              <Input 
                type="email" 
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter Email Address" 
                className="w-full mt-2" 
                autoFocus 
                required 
              />
            </div>


            <div className="mb-4">
              <Label htmlFor="password" className="block text-foreground">Password</Label>
              <Input 
                type="password" 
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter Password" 
                className="w-full mt-2" 
                required 
              />
              <p className="text-sm text-muted-foreground mt-1">
                Password must be at least 8 characters long and include a number and a special character.
              </p>
            </div>

            <div className="mb-4">
              <Label htmlFor="confirmPassword" className="block text-foreground">Confirm Password</Label>
              <Input 
                type="password" 
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm Password" 
                className="w-full mt-2" 
                required 
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="school" className="block text-foreground">School</Label>
              <select 
                id="school"
                value={school}
                onChange={(e) => setSchool(e.target.value)}
                className="w-full px-4 py-2 mt-2 border rounded-md bg-background border-input focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                required
              >
                <option value="">Select School</option>
                {schools.map((school) => (
                  <option key={school.id} value={school.id}>
                    {school.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <Label htmlFor="grade" className="block text-foreground">Grade (for students)</Label>
              <select 
                id="grade"
                value={grade}
                onChange={(e) => setGrade(e.target.value)}
                className="w-full px-4 py-2 mt-2 border rounded-md bg-background border-input focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select Grade</option>
                {Array.from({ length: 12 }, (_, i) => i + 1).map((grade) => (
                  <option key={grade} value={grade.toString()}>
                    Grade {grade}
                  </option>
                ))}
              </select>
              <p className="text-sm text-muted-foreground mt-1">
                Only required for students. Leave blank if you're not a student.
              </p>
            </div>

            <Button 
              type="submit" 
              className="w-full mt-6"
              disabled={isLoading}
            >
              {isLoading ? 'Registering...' : 'Register'}
            </Button>
          </form>

          <hr className="my-6 border-border w-full" />

          <Button 
            type="button" 
            variant="outline"
            className="w-full flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 mr-2" viewBox="0 0 48 48">
              <defs>
                <path id="a" d="M44.5 20H24v8.5h11.8C34.7 33.9 30.1 37 24 37c-7.2 0-13-5.8-13-13s5.8-13 13-13c3.1 0 5.9 1.1 8.1 2.9l6.4-6.4C34.6 4.1 29.6 2 24 2 11.8 2 2 11.8 2 24s9.8 22 22 22c11 0 21-8 21-22 0-1.3-.2-2.7-.5-4z"/>
              </defs>
              <clipPath id="b">
                <use xlinkHref="#a" overflow="visible"/>
              </clipPath>
              <path clipPath="url(#b)" fill="#FBBC05" d="M0 37V11l17 13z"/>
              <path clipPath="url(#b)" fill="#EA4335" d="M0 11l17 13 7-6.1L48 14V0H0z"/>
              <path clipPath="url(#b)" fill="#34A853" d="M0 37l30-23 7.9 1L48 0v48H0z"/>
              <path clipPath="url(#b)" fill="#4285F4" d="M48 48L17 24l-4-3 35-10z"/>
            </svg>
            Register with Google
          </Button>

          <p className="mt-8">
            Already have an account?{' '}
            <Link href="/login" className="text-primary hover:text-primary/80 font-semibold">
              Log in
            </Link>
          </p>

        </div>
      </div>
    </section>
  )
}
