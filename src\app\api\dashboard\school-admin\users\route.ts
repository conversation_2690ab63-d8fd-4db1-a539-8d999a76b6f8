import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const schoolId = url.searchParams.get('schoolId')
      const role = url.searchParams.get('role') || 'teacher'
      const limit = parseInt(url.searchParams.get('limit') || '50')

      if (!schoolId) {
        return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB
        const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })

        if (mongoUser) {
          // Check if user is a school admin
          const userRole = typeof mongoUser.role === 'object' ? mongoUser.role.slug : mongoUser.role
          if (userRole !== 'school-admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get user's school ID
          const userSchoolId =
            typeof mongoUser.school === 'object' ? mongoUser.school.id : mongoUser.school
          const userSchoolIdStr = userSchoolId ? userSchoolId.toString() : ''
          const schoolIdStr = schoolId ? schoolId.toString() : ''
          console.log('[SchoolAdminUsers] userSchoolId:', userSchoolIdStr, 'schoolId:', schoolIdStr, 'userRole:', userRole)

          // Verify the school ID matches the user's school
          if (userSchoolIdStr !== schoolIdStr) {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get users based on role and school
          const users = await db
            .collection('users')
            .find({
              $and: [
                {
                  $or: [
                    { 'school.id': schoolIdStr },
                    { school: schoolIdStr },
                  ],
                },
                {
                  $or: [
                    { 'role.slug': role },
                    { role: role },
                  ],
                },
              ],
            })
            .limit(limit)
            .toArray()

          return NextResponse.json({
            users: users.map((user) => ({
              id: user._id.toString(),
              firstName: user.firstName || '',
              lastName: user.lastName || '',
              email: user.email || '',
              role: user.role,
            })),
          })
        }
      } catch (mongoError) {
        console.warn('Error fetching users from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is a school admin
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      if (userRole !== 'school-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get user's school ID
      const userSchoolId = typeof user.school === 'object' ? user.school?.id : user.school
      const userSchoolIdStr = userSchoolId ? userSchoolId.toString() : ''
      const schoolIdStr = schoolId ? schoolId.toString() : ''
      console.log('[SchoolAdminUsers] userSchoolId:', userSchoolIdStr, 'schoolId:', schoolIdStr, 'userRole:', userRole)

      // Verify the school ID matches the user's school
      if (userSchoolIdStr !== schoolIdStr) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get users based on role and school
      const users = await payload.find({
        collection: 'users',
        where: {
          school: { equals: schoolIdStr },
          role: { equals: role },
        },
        limit,
      })

      return NextResponse.json({
        users: users.docs.map((user) => ({
          id: user.id,
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          role: user.role,
        })),
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
