'use client'

import React, { useRef, useEffect } from 'react'
import Link from 'next/link'
import '../styles/deconstructed-card.css'
import { ClientImage } from './ClientImage'
import { isMediaPath } from '@/lib/media-utils'

interface DeconstructedCardProps {
  id: string
  title: string
  subtitle?: string
  category?: string
  description?: string
  date?: string
  imageUrl?: string
  color?: string
  type?: 'article' | 'news'
}

export const DeconstructedCard: React.FC<DeconstructedCardProps> = ({
  id,
  title,
  subtitle,
  category = 'ARTICLE',
  description,
  date,
  imageUrl,
  color = 'hsl(var(--primary))',
  type = 'article',
}) => {
  const cardRef = useRef<HTMLDivElement>(null)
  const contentType = `${type}-content`

  useEffect(() => {
    const card = cardRef.current
    if (!card) return

    const handleMouseMove = (e: MouseEvent) => {
      const rect = card.getBoundingClientRect()
      const x = (e.clientX - rect.left) / rect.width
      const y = (e.clientY - rect.top) / rect.height
      const xDeg = (y - 0.5) * 8
      const yDeg = (x - 0.5) * -8

      card.style.transform = `perspective(1200px) rotateX(${xDeg}deg) rotateY(${yDeg}deg)`

      const layers = card.querySelectorAll('.card-layer')
      layers.forEach((layer, index) => {
        const depth = 30 * (index + 1)
        const translateZ = depth
        const offsetX = (x - 0.5) * 10 * (index + 1)
        const offsetY = (y - 0.5) * 10 * (index + 1)
        ;(layer as HTMLElement).style.transform =
          `translate3d(${offsetX}px, ${offsetY}px, ${translateZ}px)`
      })

      const waveSvg = card.querySelector('.wave-svg')
      if (waveSvg) {
        const moveX = (x - 0.5) * -20
        const moveY = (y - 0.5) * -20
        ;(waveSvg as HTMLElement).style.transform = `translate(${moveX}px, ${moveY}px) scale(1.05)`

        const wavePaths = waveSvg.querySelectorAll('path:not(:first-child)')
        wavePaths.forEach((path, index) => {
          const factor = 1 + index * 0.5
          const waveX = moveX * factor * 0.5
          const waveY = moveY * factor * 0.3
          ;(path as HTMLElement).style.transform = `translate(${waveX}px, ${waveY}px)`
        })
      }

      const bgObjects = card.querySelectorAll('.bg-object')
      bgObjects.forEach((obj, index) => {
        const factorX = (index + 1) * 10
        const factorY = (index + 1) * 8
        const moveX = (x - 0.5) * factorX
        const moveY = (y - 0.5) * factorY

        if (obj.classList.contains('square')) {
          ;(obj as HTMLElement).style.transform = `rotate(45deg) translate(${moveX}px, ${moveY}px)`
        } else if (obj.classList.contains('triangle')) {
          ;(obj as HTMLElement).style.transform =
            `translate(calc(-50% + ${moveX}px), calc(-50% + ${moveY}px)) scale(1)`
        } else {
          ;(obj as HTMLElement).style.transform = `translate(${moveX}px, ${moveY}px)`
        }
      })
    }

    const handleMouseLeave = () => {
      card.style.transform = ''

      const layers = card.querySelectorAll('.card-layer')
      layers.forEach((layer) => {
        ;(layer as HTMLElement).style.transform = ''
      })

      const waveSvg = card.querySelector('.wave-svg')
      if (waveSvg) {
        ;(waveSvg as HTMLElement).style.transform = ''
        const wavePaths = waveSvg.querySelectorAll('path:not(:first-child)')
        wavePaths.forEach((path) => {
          ;(path as HTMLElement).style.transform = ''
        })
      }

      const bgObjects = card.querySelectorAll('.bg-object')
      bgObjects.forEach((obj) => {
        if (obj.classList.contains('square')) {
          ;(obj as HTMLElement).style.transform = 'rotate(45deg) translateY(-20px)'
        } else if (obj.classList.contains('triangle')) {
          ;(obj as HTMLElement).style.transform = 'translate(-50%, -50%) scale(0.5)'
        } else {
          ;(obj as HTMLElement).style.transform = 'translateY(20px)'
        }
      })
    }

    card.addEventListener('mousemove', handleMouseMove)
    card.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      card.removeEventListener('mousemove', handleMouseMove)
      card.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [])

  // Generate a safe ID for the link - if it's a media path, generate a slug from the title
  const safeId = isMediaPath(id)
    ? title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    : id

  // Log if we're dealing with a media path
  if (isMediaPath(id)) {
    console.log(`Generated ID for article without valid ID: ${safeId}`)
  }

  return (
    <Link href={`/${type}s/${safeId}`} className="block">
      <article ref={cardRef} className="deconstructed-card">
        <div className="card-layer card-image">
          <div className="image-background-container">
            {/* Use an actual image element instead of background-image for better error handling */}
            <ClientImage
              src={imageUrl || 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80'}
              alt={title}
              className="absolute inset-0 w-full h-full object-cover"
              fallbackSrc="https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80"
            />
            {/* Color overlay */}
            <div
              className="absolute inset-0"
              style={{ backgroundColor: color, opacity: 0.3 }}
            />
            {/* Debug info */}
            {process.env.NODE_ENV === 'development' && (
              <div className="absolute bottom-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1">
                {imageUrl ? 'Has Image' : 'No Image'}
              </div>
            )}
            <div className="color-overlay" style={{ backgroundColor: `${color}80` }}></div>
            <svg className="wave-svg" viewBox="0 0 300 400" preserveAspectRatio="none">
              <path
                d="M0,230 C30,220 60,240 90,230 C120,220 150,240 180,230 C210,220 240,240 270,230 C290,225 295,230 300,225 L300,400 L0,400 Z"
                fill={`${color}90`}
                opacity="0.5"
              />
              <path
                d="M0,260 C40,250 80,270 120,260 C160,250 200,270 240,260 C280,250 290,260 300,255 L300,400 L0,400 Z"
                fill={`${color}70`}
                opacity="0.4"
              />
              <path
                d="M0,290 C50,280 100,300 150,290 C200,280 250,300 300,290 L300,400 L0,400 Z"
                fill={`${color}50`}
                opacity="0.3"
              />
            </svg>
          </div>
        </div>
        <div className="card-layer card-frame">
          <svg viewBox="0 0 300 400" preserveAspectRatio="none">
            <path className="frame-path" d="M 20,20 H 280 V 380 H 20 Z" />
          </svg>
        </div>
        <div className={`card-layer card-content ${contentType}`}>
          <div className="content-fragment fragment-heading">
            <h2 className="content-text content-title">{title}</h2>
          </div>
          <div className="content-fragment fragment-meta">
            <div className="meta-line"></div>
            <span className="meta-text">{category.toUpperCase()}</span>
          </div>
          <div className="content-fragment fragment-cta">
            <div className="cta-link">
              <div className="cta-box"></div>
              <span className="cta-text">READ MORE</span>
            </div>
          </div>
        </div>
      </article>
    </Link>
  )
}
