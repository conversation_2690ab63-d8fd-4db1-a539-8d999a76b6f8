import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import Image from 'next/image'
import { getPayload } from 'payload'
import React from 'react'

import config from '@/payload.config'
import { PageLayout } from '@/components/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { BookOpen, Users, School } from 'lucide-react'

export default async function AboutPage() {
  const headers = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  // Authenticate but we don't need the user object for this page
  await payload.auth({ headers })

  return (
    <PageLayout
      bgImage="bg-[url(https://images.unsplash.com/photo-1633360821222-7e8df83639fb?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)]"
      title="About Young Reporter"
      subtitle="Learn about our mission and how we're empowering student journalists"
      bgColor="bg-violet-700"
    >
      {/* About Content */}
      <div className="py-12 px-4 bg-muted/30 p-8 max-w-7xl mx-auto">
        <div className="container mx-auto">
          <div className="mb-12 overflow-hidden rounded-lg  ">
            <div className="flex flex-col md:flex-row">
              {/* Text Side */}
              <div className="w-full md:w-1/2 p-8 flex flex-col justify-center py-12">
                <h2 className="text-5xl  font-bold mb-6 text-primary">Our Mission</h2>
                <p className="text-lg mb-4">
                  Young Reporter is dedicated to empowering student journalists by providing a
                  platform for them to share their voices, develop their writing skills, and receive
                  valuable feedback from experienced teachers and mentors.
                </p>
                <p className="text-lg mb-4">
                  We believe that every student has a unique perspective and story to tell. Our
                  mission is to nurture these voices and help them reach a wider audience.
                </p>
                <p className="text-lg">
                  Through our platform, we aim to create the next generation of thoughtful, skilled,
                  and confident journalists who can make a positive impact on their communities and
                  beyond.
                </p>
              </div>

              {/* Image Side */}
              <div className="w-full md:w-1/2 relative min-h-[300px] md:min-h-0">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent z-10"></div>
                <Image
                  src="/media/2 (4) (2) - Copy.png"
                  alt="Young journalists working together"
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                  priority
                  className="object-contain"
                  style={{ objectPosition: 'center' }}
                />
              </div>
            </div>
          </div>

          <h2 className="text-3xl font-bold mb-8 text-center">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <Card className="border-t-4 border-primary overflow-hidden transition-all duration-200 hover:shadow-lg">
              <CardHeader className="text-center pb-2">
                <BookOpen className="w-12 h-12 mx-auto text-primary mb-4" />
                <CardTitle>For Students</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  <li>Write and submit articles on topics you&apos;re passionate about</li>
                  <li>Receive feedback and ratings from teachers</li>
                  <li>Get your work published and shared with a wider audience</li>
                  <li>Build a portfolio of published work</li>
                  <li>Compete for top spots on the student leaderboard</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-t-4 border-green-500 overflow-hidden transition-all duration-200 hover:shadow-lg">
              <CardHeader className="text-center pb-2">
                <Users className="w-12 h-12 mx-auto text-green-500 mb-4" />
                <CardTitle>For Teachers</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  <li>Review student articles and provide constructive feedback</li>
                  <li>Rate articles and approve them for publication</li>
                  <li>Help students develop their writing and journalism skills</li>
                  <li>Track your impact through the teacher leaderboard</li>
                  <li>Collaborate with other educators to improve student outcomes</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-t-4 border-amber-500 overflow-hidden transition-all duration-200 hover:shadow-lg">
              <CardHeader className="text-center pb-2">
                <School className="w-12 h-12 mx-auto text-amber-500 mb-4" />
                <CardTitle>For Schools</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                  <li>Showcase your students&apos; best work</li>
                  <li>Foster a culture of writing and journalism</li>
                  <li>Track your school&apos;s performance on the leaderboard</li>
                  <li>Connect with other schools and share best practices</li>
                  <li>Celebrate student achievements and progress</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <Card className="mb-12">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold mb-6">Our Team</h2>
              <p className="text-lg mb-6">
                Young Reporter was founded by a team of educators, journalists, and technologists
                who believe in the power of student voices. Our platform is designed to create a
                supportive environment where students can develop their writing skills, receive
                constructive feedback, and build confidence in their abilities.
              </p>
            </CardContent>
          </Card>

          <Card className="mb-12">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold mb-6">Get Involved</h2>
              <p className="text-lg mb-6">
                Whether you&apos;re a student looking to share your voice, a teacher wanting to
                mentor young writers, or a school administrator interested in bringing Young
                Reporter to your institution, we&apos;d love to hear from you. Contact us to learn
                more about how you can get involved.
              </p>

              <div className="mt-8 text-center">
                <Button asChild size="lg" className="px-8">
                  <Link href="/contact">Contact Us</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  )
}
