import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const limit = parseInt(url.searchParams.get('limit') || '10')

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB
        const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })

        if (mongoUser) {
          // Get user role slug
          let userRoleSlug: string | null = null
          console.log('User object:', mongoUser)
          console.log('User role property:', mongoUser.role)

          if (mongoUser.role instanceof ObjectId) {
            // If role is an ObjectId, use it to lookup the role
            console.log('User role is ObjectId, looking up role by ID:', mongoUser.role)
            const role = await db.collection('roles').findOne({ _id: mongoUser.role })
            console.log('Role document found:', role)
            userRoleSlug = role?.slug || null
          } else if (typeof mongoUser.role === 'string' && ObjectId.isValid(mongoUser.role)) {
            // If role is a string ID, fetch the role and use its slug
            console.log('User role is string ID, looking up role by ID:', mongoUser.role)
            try {
              const { db } = await connectToDatabase() // Re-connect if needed, or use existing connection
              const role = await db
                .collection('roles')
                .findOne({ _id: new ObjectId(mongoUser.role) })
              userRoleSlug = role?.slug || null
            } catch (error) {
              console.error('Error fetching role for school admin dashboard:', error)
              userRoleSlug = null // Ensure slug is null on error
            }
          } else if (
            typeof mongoUser.role === 'object' &&
            mongoUser.role !== null &&
            'slug' in mongoUser.role
          ) {
            // If role is a populated object, use its slug
            userRoleSlug = mongoUser.role.slug
          }

          console.log('Checking access for userRoleSlug:', userRoleSlug)
          // Check if user is a school admin
          if (userRoleSlug !== 'school-admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get school ID
          const schoolId =
            typeof mongoUser.school === 'object' &&
            mongoUser.school !== null &&
            'id' in mongoUser.school
              ? mongoUser.school.id
              : mongoUser.school

          if (!schoolId) {
            return NextResponse.json(
              { error: 'School admin must be associated with a school' },
              { status: 400 },
            )
          }

          // Get the ObjectIds for roles
          const studentRoleDoc = await db.collection('roles').findOne({ slug: 'student' })
          const teacherRoleDoc = await db.collection('roles').findOne({ slug: 'teacher' })
          const mentorRoleDoc = await db.collection('roles').findOne({ slug: 'mentor' })

          // Get counts
          const usersCount = await db.collection('users').countDocuments({
            $or: [
              { 'school.id': schoolId },
              { school: schoolId },
              { school: new ObjectId(schoolId) },
            ],
          })
          const studentsCount = await db.collection('users').countDocuments({
            $and: [
              {
                $or: [
                  studentRoleDoc ? { role: studentRoleDoc._id } : {},
                  { role: 'student' },
                  { 'role.slug': 'student' },
                ],
              },
              {
                $or: [
                  { 'school.id': schoolId },
                  { school: schoolId },
                  { school: new ObjectId(schoolId) },
                ],
              },
            ],
          })
          const teachersCount = await db.collection('users').countDocuments({
            $and: [
              {
                $or: [
                  teacherRoleDoc ? { role: teacherRoleDoc._id } : {},
                  { role: 'teacher' },
                  { 'role.slug': 'teacher' },
                ],
              },
              {
                $or: [
                  { 'school.id': schoolId },
                  { school: schoolId },
                  { school: new ObjectId(schoolId) },
                ],
              },
            ],
          })
          const mentorsCount = await db.collection('users').countDocuments({
            $and: [
              {
                $or: [
                  mentorRoleDoc ? { role: mentorRoleDoc._id } : {},
                  { role: 'mentor' },
                  { 'role.slug': 'mentor' },
                ],
              },
              {
                $or: [
                  { 'school.id': schoolId },
                  { school: schoolId },
                  { school: new ObjectId(schoolId) },
                ],
              },
            ],
          })
          const articlesCount = await db.collection('articles').countDocuments({
            $or: [
              { 'author.school.id': schoolId },
              { 'author.school': schoolId },
              { school: schoolId },
              { 'school.id': schoolId },
              { school: new ObjectId(schoolId) },
            ],
          })

          // Get pending approvals
          const pendingApprovals = await db
            .collection('approvalRequests')
            .find({
              status: 'pending',
              schoolId: schoolId,
            })
            .limit(limit)
            .toArray()

          // Get leaderboard data
          const studentLeaders = await db
            .collection('users')
            .find({
              $or: [
                studentRoleDoc ? { role: studentRoleDoc._id } : {},
                { role: 'student' },
                { 'role.slug': 'student' },
              ],
              points: { $exists: true, $ne: null },
            })
            .sort({ points: -1 })
            .limit(10)
            .toArray()
            .then((students) =>
              students.map((student: any) => ({
                id: student._id.toString(),
                name: `${student.firstName} ${student.lastName}`,
                score: student.points || 0,
                type: 'student',
              })),
            )

          const teacherLeaders = await db
            .collection('users')
            .find({
              $or: [
                teacherRoleDoc ? { role: teacherRoleDoc._id } : {},
                { role: 'teacher' },
                { 'role.slug': 'teacher' },
              ],
              points: { $exists: true, $ne: null },
            })
            .sort({ points: -1 })
            .limit(10)
            .toArray()
            .then((teachers) =>
              teachers.map((teacher: any) => ({
                id: teacher._id.toString(),
                name: `${teacher.firstName} ${teacher.lastName}`,
                score: teacher.points || 0,
                type: 'teacher',
              })),
            )

          const mentorLeaders = await db
            .collection('users')
            .find({
              $or: [
                mentorRoleDoc ? { role: mentorRoleDoc._id } : {},
                { role: 'mentor' },
                { 'role.slug': 'mentor' },
              ],
              points: { $exists: true, $ne: null },
            })
            .sort({ points: -1 })
            .limit(10)
            .toArray()
            .then((mentors) =>
              mentors.map((mentor: any) => ({
                id: mentor._id.toString(),
                name: `${mentor.firstName} ${mentor.lastName}`,
                score: mentor.points || 0,
                type: 'mentor',
              })),
            )

          // Combine all leaders
          const leaderboard = [...studentLeaders, ...teacherLeaders, ...mentorLeaders]

          // Get mentor review quality scores
          const mentorReviews: any[] = await db // Explicitly type mentorReviews as any[]
            .collection('articles')
            .aggregate([
              { $match: { 'teacherReview.reviewer': { $exists: true } } },
              { $unwind: '$teacherReview' },
              {
                $lookup: {
                  from: 'users',
                  localField: 'teacherReview.reviewer',
                  foreignField: '_id',
                  as: 'reviewer',
                },
              },
              { $unwind: '$reviewer' },
              {
                $match: {
                  'reviewer.role.slug': 'mentor',
                },
              },
              {
                $group: {
                  _id: '$reviewer._id',
                  mentorName: {
                    $first: { $concat: ['$reviewer.firstName', ' ', '$reviewer.lastName'] },
                  },
                  totalReviews: { $sum: 1 },
                  totalRating: { $sum: '$teacherReview.rating' },
                  lastReviewDate: { $max: '$teacherReview.reviewDate' },
                },
              },
              {
                $project: {
                  mentorId: { $toString: '$_id' },
                  mentorName: 1,
                  totalReviews: 1,
                  averageRating: { $divide: ['$totalRating', '$totalReviews'] },
                  lastReviewDate: 1,
                  _id: 0,
                },
              },
            ])
            .toArray()

          // Get resource utilization metrics
          const resourceMetrics = [
            {
              name: 'Storage Usage',
              value: Math.floor(Math.random() * 75), // Mock data - replace with actual metrics
              maxValue: 100,
              unit: 'GB',
            },
            {
              name: 'API Calls',
              value: Math.floor(Math.random() * 8000), // Mock data - replace with actual metrics
              maxValue: 10000,
              unit: 'calls/day',
            },
            {
              name: 'User Sessions',
              value: Math.floor(Math.random() * 150), // Mock data - replace with actual metrics
              maxValue: 200,
              unit: 'concurrent',
            },
          ]

          // Create dashboard data
          const dashboardData = {
            stats: {
              users: usersCount,
              students: studentsCount,
              teachers: teachersCount,
              mentors: mentorsCount,
              articles: articlesCount,
              pendingApprovals: pendingApprovals.length,
              resourceUsage: 65, // Mock data - replace with actual metrics
            },
            leaderboard,
            pendingApprovals: pendingApprovals.map((approval: any) => ({
              // Explicitly type approval as any
              id: approval._id.toString(),
              type: approval.type,
              requesterId: approval.requesterId,
              requesterName: approval.requesterName || 'Unknown User',
              requesterRole: approval.requesterRole || 'Unknown Role',
              status: approval.status,
              createdAt: approval.createdAt,
              data: approval.data,
            })),
            mentorReviews, // Explicitly typed above
            resourceMetrics,
          }

          return NextResponse.json(dashboardData)
        }
      } catch (mongoError) {
        console.warn(
          'Error fetching school admin data from MongoDB, falling back to Payload:',
          mongoError,
        )
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is a school admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'school-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get school ID
      const schoolId = typeof user.school === 'object' ? user.school?.id : user.school

      if (!schoolId) {
        return NextResponse.json(
          { error: 'School admin must be associated with a school' },
          { status: 400 },
        )
      }

      // Get counts using Payload CMS
      const usersCount = await payload.find({
        collection: 'users',
        where: { school: { equals: schoolId } },
        limit: 0,
      })

      const studentsCount = await payload.find({
        collection: 'users',
        where: {
          school: { equals: schoolId },
          role: { equals: 'student' },
        },
        limit: 0,
      })

      const teachersCount = await payload.find({
        collection: 'users',
        where: {
          school: { equals: schoolId },
          role: { equals: 'teacher' },
        },
        limit: 0,
      })

      const mentorsCount = await payload.find({
        collection: 'users',
        where: {
          school: { equals: schoolId },
          role: { equals: 'mentor' },
        },
        limit: 0,
      })

      const articlesCount = await payload.find({
        collection: 'articles',
        where: { 'author.school': { equals: schoolId } },
        limit: 0,
      })

      // Get pending approvals
      let pendingApprovals = []

      // Check if the approvalRequests collection exists in MongoDB
      try {
        const { db } = await connectToDatabase()

        // Check if the collection exists
        const collections = await db.listCollections({ name: 'approvalRequests' }).toArray()

        if (collections.length > 0) {
          // Collection exists, fetch data
          pendingApprovals = await db
            .collection('approvalRequests')
            .find({
              status: 'pending',
              schoolId: schoolId,
            })
            .limit(limit)
            .toArray()
            .then((requests) =>
              requests.map((request) => ({
                id: request._id.toString(),
                type: request.type || 'unknown',
                requesterId: request.requesterId || '',
                requesterName: request.requesterName || 'Unknown User',
                requesterRole: request.requesterRole || 'Unknown Role',
                status: request.status || 'pending',
                createdAt: request.createdAt || new Date().toISOString(),
                data: request.data || {},
              })),
            )
        } else {
          console.log('approvalRequests collection does not exist in MongoDB')
          // Use mock data for development
          pendingApprovals = []
        }
      } catch (mongoError) {
        console.warn('Error fetching approval requests from MongoDB:', mongoError)

        // Fallback to Payload CMS
        try {
          // Try to access the collection directly
          let hasApprovalRequests = false

          try {
            // Use any to bypass TypeScript checking for custom collections
            const payloadAny = payload as any
            // Try to access the collection schema
            const schema = payloadAny.collections?.approvalRequests?.config?.fields
            hasApprovalRequests = !!schema
          } catch (error) {
            console.log('Error checking for approvalRequests collection:', error)
            hasApprovalRequests = false
          }

          if (hasApprovalRequests) {
            // Use any to bypass TypeScript checking for custom collections
            const payloadAny = payload as any
            const approvalRequests = await payloadAny.find({
              collection: 'approvalRequests',
              where: {
                status: { equals: 'pending' },
                schoolId: { equals: schoolId },
              },
              limit,
            })
            pendingApprovals = approvalRequests.docs || []
          } else {
            console.log('approvalRequests collection does not exist in Payload CMS')
            // Use mock data for development
            pendingApprovals = []
          }
        } catch (payloadError) {
          console.warn('Error fetching approval requests from Payload:', payloadError)
          // Use empty array as fallback
          pendingApprovals = []
        }
      }

      // Get leaderboard data
      const students = await payload.find({
        collection: 'users',
        where: {
          school: { equals: schoolId },
          role: { equals: 'student' },
        },
        sort: '-points',
        limit: 10,
      })

      const teachers = await payload.find({
        collection: 'users',
        where: {
          school: { equals: schoolId },
          role: { equals: 'teacher' },
        },
        sort: '-points',
        limit: 10,
      })

      const mentors = await payload.find({
        collection: 'users',
        where: {
          school: { equals: schoolId },
          role: { equals: 'mentor' },
        },
        sort: '-points',
        limit: 10,
      })

      // Format leaderboard data
      const studentLeaders = students.docs.map((student: any) => ({
        // Explicitly type student as any for now
        id: student.id,
        name: `${student.firstName || ''} ${student.lastName || ''}`.trim(),
        score: student.points || 0,
        type: 'student',
      }))

      const teacherLeaders = teachers.docs.map((teacher: any) => ({
        // Explicitly type teacher as any for now
        id: teacher.id,
        name: `${teacher.firstName || ''} ${teacher.lastName || ''}`.trim(),
        score: teacher.points || 0,
        type: 'teacher',
      }))

      const mentorLeaders = mentors.docs.map((mentor: any) => ({
        // Explicitly type mentor as any for now
        id: mentor.id,
        name: `${mentor.firstName || ''} ${mentor.lastName || ''}`.trim(),
        score: mentor.points || 0,
        type: 'mentor',
      }))

      // Combine all leaders
      const leaderboard = [...studentLeaders, ...teacherLeaders, ...mentorLeaders]

      // Get mentor review quality scores
      // This is a simplified version - in a real implementation, you would need to
      // aggregate data from articles and calculate average ratings
      const mentorReviews: any[] = [] // Explicitly type mentorReviews as any[]

      // Get all articles with teacher reviews
      const articles = await payload.find({
        collection: 'articles',
        where: { 'author.school': { equals: schoolId } },
        depth: 2,
      })

      // Process articles to extract mentor review data
      const mentorReviewMap = new Map()

      articles.docs.forEach((article) => {
        if (article.teacherReview && Array.isArray(article.teacherReview)) {
          article.teacherReview.forEach((review) => {
            // Check if the reviewer is a mentor
            if (review.reviewer && typeof review.reviewer === 'object') {
              const reviewerRole =
                typeof review.reviewer.role === 'object'
                  ? review.reviewer.role?.slug
                  : review.reviewer.role

              if (reviewerRole === 'mentor') {
                const mentorId = review.reviewer.id
                const mentorName =
                  `${review.reviewer.firstName || ''} ${review.reviewer.lastName || ''}`.trim()
                const rating = review.rating || 0
                const reviewDate = (review as any).reviewDate || new Date().toISOString() // Cast to any

                if (!mentorReviewMap.has(mentorId)) {
                  mentorReviewMap.set(mentorId, {
                    mentorId,
                    mentorName,
                    totalReviews: 0,
                    totalRating: 0,
                    lastReviewDate: reviewDate,
                  })
                }

                const mentorData = mentorReviewMap.get(mentorId)
                mentorData.totalReviews += 1
                mentorData.totalRating += rating

                // Update last review date if this review is more recent
                if (new Date(reviewDate) > new Date(mentorData.lastReviewDate)) {
                  mentorData.lastReviewDate = reviewDate
                }
              }
            }
          })
        }
      })

      // Calculate average ratings and format mentor review data
      mentorReviewMap.forEach((mentorData) => {
        mentorReviews.push({
          mentorId: mentorData.mentorId,
          mentorName: mentorData.mentorName,
          totalReviews: mentorData.totalReviews,
          averageRating:
            mentorData.totalReviews > 0 ? mentorData.totalRating / mentorData.totalReviews : 0,
          lastReviewDate: mentorData.lastReviewDate,
        })
      })

      // Get resource utilization metrics (mock data - replace with actual metrics)
      const resourceMetrics = [
        {
          name: 'Storage Usage',
          value: Math.floor(Math.random() * 75),
          maxValue: 100,
          unit: 'GB',
        },
        {
          name: 'API Calls',
          value: Math.floor(Math.random() * 8000),
          maxValue: 10000,
          unit: 'calls/day',
        },
        {
          name: 'User Sessions',
          value: Math.floor(Math.random() * 150),
          maxValue: 200,
          unit: 'concurrent',
        },
      ]

      // Format pending approvals
      const formattedPendingApprovals = pendingApprovals.map((approval: any) => ({
        // Explicitly type approval as any
        id: approval.id,
        type: approval.type,
        requesterId: approval.requesterId,
        requesterName: approval.requesterName || 'Unknown User',
        requesterRole: approval.requesterRole || 'Unknown Role',
        status: approval.status,
        createdAt: approval.createdAt,
        data: approval.data,
      }))

      // Create dashboard data
      const dashboardData = {
        stats: {
          users: usersCount.totalDocs,
          students: studentsCount.totalDocs,
          teachers: teachersCount.totalDocs,
          mentors: mentorsCount.totalDocs,
          articles: articlesCount.totalDocs,
          pendingApprovals: pendingApprovals.length,
          resourceUsage: 65, // Mock data - replace with actual metrics
        },
        leaderboard,
        pendingApprovals: formattedPendingApprovals,
        mentorReviews, // Explicitly typed above
        resourceMetrics,
      }

      return NextResponse.json(dashboardData)
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching school admin dashboard data:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
