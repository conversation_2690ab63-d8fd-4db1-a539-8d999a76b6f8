'use client'

import { useEffect, useState } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { CheckCircle, AlertCircle, Eye, Pencil, Trash2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

// Add a Student interface
interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  grade?: string;
  status: string;
  school?: {
    id: string;
    name: string;
  };
}

export default function StudentsPage() {
  const { toast } = useToast()
  const [students, setStudents] = useState<Student[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [isProcessing, setIsProcessing] = useState<Record<string, boolean>>({})
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    grade: '',
  })

  const fetchStudents = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/dashboard/students', {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch students')
      }

      const data = await response.json()
      setStudents(data.students || [])
      setIsLoading(false)
    } catch (err) {
      console.error('Error fetching students:', err)
      setError('فشل في تحميل بيانات الطلاب. يرجى المحاولة مرة أخرى.')
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchStudents()
  }, [])

  const handleApproval = async (studentId: string, action: 'approve' | 'reject') => {
    try {
      setIsProcessing({ ...isProcessing, [studentId + action]: true })

      const response = await fetch('/api/dashboard/student-approvals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          studentId,
          action,
          approvalType: 'account',
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to process approval')
      }

      // Refresh the list
      fetchStudents()
    } catch (err) {
      console.error('Error processing approval:', err)
      alert('فشل في معالجة الموافقة. يرجى المحاولة مرة أخرى.')
    } finally {
      setIsProcessing({ ...isProcessing, [studentId + action]: false })
    }
  }

  const handleViewStudent = (student: Student) => {
    setSelectedStudent(student)
    setShowViewDialog(true)
  }

  const handleEditStudent = (student: Student) => {
    setSelectedStudent(student)
    setEditForm({
      firstName: student.firstName,
      lastName: student.lastName,
      email: student.email,
      grade: student.grade || '',
    })
    setShowEditDialog(true)
  }

  const handleDeleteStudent = (student: Student) => {
    setSelectedStudent(student)
    setShowDeleteDialog(true)
  }

  const confirmDeleteStudent = async () => {
    if (!selectedStudent) return
    
    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/dashboard/users/${selectedStudent.id}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete student')
      }

      toast({
        title: 'تم حذف الطالب',
        description: `تم حذف ${selectedStudent.firstName} ${selectedStudent.lastName} بنجاح.`,
      })

      // Refresh the student list
      fetchStudents()
      setShowDeleteDialog(false)
    } catch (err: any) {
      toast({
        title: 'خطأ',
        description: err.message || 'فشل في حذف الطالب. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const submitEditForm = async () => {
    if (!selectedStudent) return
    
    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/dashboard/users/${selectedStudent.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(editForm),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update student')
      }

      toast({
        title: 'تم تحديث معلومات الطالب',
        description: `تم تحديث معلومات ${editForm.firstName} ${editForm.lastName} بنجاح.`,
      })

      // Refresh the student list
      fetchStudents()
      setShowEditDialog(false)
    } catch (err: any) {
      toast({
        title: 'خطأ',
        description: err.message || 'فشل في تحديث معلومات الطالب. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" dir="rtl">
          {error}
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6" dir="rtl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">طلابي</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>جميع الطلاب</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الاسم</TableHead>
                  <TableHead>البريد الإلكتروني</TableHead>
                  <TableHead>الصف</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {students.length > 0 ? (
                  students.map((student: any) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        {student.firstName} {student.lastName}
                      </TableCell>
                      <TableCell>{student.email}</TableCell>
                      <TableCell>{student.grade || 'غير متوفر'}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            student.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : student.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {student.status === 'active' ? 'نشط' : 
                           student.status === 'pending' ? 'قيد الانتظار' : 
                           student.status === 'inactive' ? 'غير نشط' : student.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2 space-x-reverse">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleViewStudent(student)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEditStudent(student)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="text-red-500"
                            onClick={() => handleDeleteStudent(student)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          {student.status === 'pending' && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                className="bg-green-50 text-green-600 border-green-200 hover:bg-green-100 hover:text-green-700"
                                onClick={() => handleApproval(student.id, 'approve')}
                                disabled={isProcessing[student.id + 'approve']}
                              >
                                {isProcessing[student.id + 'approve'] ? (
                                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-green-600 border-t-transparent" />
                                ) : (
                                  <CheckCircle className="h-4 w-4 ml-1" />
                                )}
                                قبول
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="bg-red-50 text-red-600 border-red-200 hover:bg-red-100 hover:text-red-700"
                                onClick={() => handleApproval(student.id, 'reject')}
                                disabled={isProcessing[student.id + 'reject']}
                              >
                                {isProcessing[student.id + 'reject'] ? (
                                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent" />
                                ) : (
                                  <AlertCircle className="h-4 w-4 ml-1" />
                                )}
                                رفض
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      لا يوجد طلاب
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* View Student Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="sm:max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle>تفاصيل الطالب</DialogTitle>
            <DialogDescription>
              عرض معلومات مفصلة عن هذا الطالب.
            </DialogDescription>
          </DialogHeader>
          {selectedStudent && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">الاسم الأول</Label>
                  <p className="font-medium">{selectedStudent.firstName}</p>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">الاسم الأخير</Label>
                  <p className="font-medium">{selectedStudent.lastName}</p>
                </div>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">البريد الإلكتروني</Label>
                <p className="font-medium">{selectedStudent.email}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">الصف</Label>
                <p className="font-medium">{selectedStudent.grade || 'غير متوفر'}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">الحالة</Label>
                <p className="font-medium">
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${
                      selectedStudent.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : selectedStudent.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {selectedStudent.status === 'active' ? 'نشط' : 
                     selectedStudent.status === 'pending' ? 'قيد الانتظار' : 
                     selectedStudent.status === 'inactive' ? 'غير نشط' : selectedStudent.status}
                  </span>
                </p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">المدرسة</Label>
                <p className="font-medium">
                  {selectedStudent.school?.name || 'غير متوفر'}
                </p>
              </div>
            </div>
          )}
          <DialogFooter className="flex-row-reverse sm:justify-start">
            <DialogClose asChild>
              <Button type="button" variant="secondary">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Student Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle>تعديل بيانات الطالب</DialogTitle>
            <DialogDescription>
              قم بإجراء تغييرات على معلومات الطالب.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="firstName">الاسم الأول</Label>
              <Input
                id="firstName"
                value={editForm.firstName}
                onChange={(e) => setEditForm({ ...editForm, firstName: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">الاسم الأخير</Label>
              <Input
                id="lastName"
                value={editForm.lastName}
                onChange={(e) => setEditForm({ ...editForm, lastName: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input
                id="email"
                value={editForm.email}
                onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="grade">الصف</Label>
              <Input
                id="grade"
                value={editForm.grade}
                onChange={(e) => setEditForm({ ...editForm, grade: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter className="flex-row-reverse sm:justify-start">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setShowEditDialog(false)}
            >
              إلغاء
            </Button>
            <Button 
              onClick={submitEditForm}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جارٍ الحفظ...' : 'حفظ التغييرات'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Student Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle>تأكيد الحذف</DialogTitle>
            <DialogDescription>
              هل أنت متأكد أنك تريد حذف هذا الطالب؟ هذا الإجراء لا يمكن التراجع عنه.
            </DialogDescription>
          </DialogHeader>
          {selectedStudent && (
            <div className="py-3">
              <p><strong>الاسم:</strong> {selectedStudent.firstName} {selectedStudent.lastName}</p>
              <p><strong>البريد الإلكتروني:</strong> {selectedStudent.email}</p>
              <p><strong>الحالة:</strong> {selectedStudent.status === 'active' ? 'نشط' : 
                                           selectedStudent.status === 'pending' ? 'قيد الانتظار' : 
                                           selectedStudent.status === 'inactive' ? 'غير نشط' : selectedStudent.status}</p>
            </div>
          )}
          <DialogFooter className="flex-row-reverse sm:justify-start">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setShowDeleteDialog(false)}
            >
              إلغاء
            </Button>
            <Button 
              variant="destructive"
              onClick={confirmDeleteStudent}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جارٍ الحذف...' : 'حذف الطالب'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  )
}
