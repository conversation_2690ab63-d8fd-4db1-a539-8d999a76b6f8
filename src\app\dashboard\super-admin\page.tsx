'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { GlobalActivityHeatmap } from './components/global-activity-heatmap'
import { CrossSchoolStatistics } from './components/cross-school-statistics'
import { RoleDistributionChart } from './components/role-distribution-chart'
import { RawAuditLogs } from './components/raw-audit-logs'
import { SchoolsManager } from './components/schools-manager'
import { ContentManager } from './components/content-manager'
import { ReportsExporter } from './components/reports-exporter'
import { FeedbackManager } from './components/feedback-manager'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'

export default function SuperAdminDashboard() {
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    // Check if the user is authorized as super-admin
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/dashboard/super-admin/validate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            checks: ['dashboard access'],
          }),
        })

        if (!response.ok) {
          const data = await response.json()
          setError(data.message || 'You do not have permission to access this page')
          setIsAuthorized(false)
          return
        }

        setIsAuthorized(true)
      } catch (error) {
        console.error('Error checking super-admin authorization:', error)
        setError('Failed to verify permissions')
        setIsAuthorized(false)
      }
    }

    checkAuth()
    
    // Check if there's a tab in the URL hash
    const hash = window.location.hash.replace('#', '')
    if (hash && ['overview', 'management', 'content'].includes(hash)) {
      setActiveTab(hash)
    } else {
      // Default to overview if hash is 'system' (which is now hidden) or invalid
      setActiveTab('overview')
    }
  }, [])
  
  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    window.location.hash = value
  }

  if (isAuthorized === null) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>
  }

  if (isAuthorized === false) {
    return (
      <div className="container mx-auto py-10">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>{error || 'You do not have permission to access this page'}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-10 space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">Super Admin Dashboard</h1>
        <p className="text-muted-foreground">Complete system access and management tools</p>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="grid grid-cols-3 mb-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="management">Management</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          {/* System tab commented out as requested */}
          {/* <TabsTrigger value="system">System</TabsTrigger> */}
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>Global Activity Heatmap</CardTitle>
                <CardDescription>Activity across all schools over time</CardDescription>
              </CardHeader>
              <CardContent>
                <GlobalActivityHeatmap />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cross-School Statistics</CardTitle>
                <CardDescription>Comparative performance metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <CrossSchoolStatistics />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Role Distribution</CardTitle>
                <CardDescription>User role breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <RoleDistributionChart />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="management" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Schools Management</CardTitle>
              <CardDescription>Create, edit and manage schools</CardDescription>
            </CardHeader>
            <CardContent>
              <SchoolsManager />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Global Reports</CardTitle>
              <CardDescription>Export system-wide data</CardDescription>
            </CardHeader>
            <CardContent>
              <ReportsExporter />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Feedback Management</CardTitle>
              <CardDescription>View and respond to all feedback across schools</CardDescription>
            </CardHeader>
            <CardContent>
              <FeedbackManager />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Content Management</CardTitle>
              <CardDescription>Manage articles, news and comments across schools</CardDescription>
            </CardHeader>
            <CardContent>
              <ContentManager />
            </CardContent>
          </Card>
        </TabsContent>

        {/* System tab content commented out as requested */}
        {/* 
        <TabsContent value="system" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Role Distribution</CardTitle>
                <CardDescription>User role breakdown across the system</CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <RoleDistributionChart />
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Audit Logs</CardTitle>
                <CardDescription>System-wide action logs</CardDescription>
              </CardHeader>
              <CardContent>
                <RawAuditLogs />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        */}
      </Tabs>
    </div>
  )
} 