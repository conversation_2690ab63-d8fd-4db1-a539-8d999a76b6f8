import { notFound } from 'next/navigation'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import SchoolTabs from '../SchoolTabs'
import { cookies } from 'next/headers'

async function getSchool(id: string) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
  const cookieStore = await cookies()
  const cookieHeader = cookieStore.toString()
  const res = await fetch(`${baseUrl}/api/dashboard/super-admin/schools/${id}`, {
    cache: 'no-store',
    headers: { cookie: cookieHeader },
  })
  if (!res.ok) return null
  return res.json()
}

export default async function SchoolDetailsPage({ params }: { params: { id: string } }) {
  const school = await getSchool((await params).id)
  if (!school) return notFound()
  return (
    <DashboardLayout>
      <div className="max-w-5xl mx-auto py-8">
        <h1 className="text-2xl font-bold mb-2">{school.name}</h1>
        <div className="mb-6 text-muted-foreground">{school.address}</div>
        <SchoolTabs schoolId={school.id} school={school} />
      </div>
    </DashboardLayout>
  )
}
