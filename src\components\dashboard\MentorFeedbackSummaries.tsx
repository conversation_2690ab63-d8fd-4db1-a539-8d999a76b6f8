'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { MessageSquare, AlertCircle, ThumbsUp, ThumbsDown } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { formatDistanceToNow } from 'date-fns'
import { ar } from 'date-fns/locale'

interface MentorFeedback {
  id: string
  mentorId: string
  mentorName: string
  teacherId: string
  teacherName: string
  articleId: string
  articleTitle: string
  sentiment: 'positive' | 'negative' | 'neutral'
  summary: string
  createdAt: string
}

interface MentorFeedbackSummariesProps {
  schoolId: string
}

export default function MentorFeedbackSummaries({ schoolId }: MentorFeedbackSummariesProps) {
  const [mentorFeedback, setMentorFeedback] = useState<MentorFeedback[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchMentorFeedback() {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/dashboard/school-admin/mentor-feedback?schoolId=${schoolId}`)
        
        if (!response.ok) {
          throw new Error('فشل في جلب ملخصات تعليقات الموجهين')
        }
        
        const data = await response.json()
        setMentorFeedback(data.mentorFeedback || [])
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching mentor feedback:', err)
        setError('فشل في تحميل ملخصات تعليقات الموجهين')
        setIsLoading(false)
      }
    }
    
    fetchMentorFeedback()
  }, [schoolId])
  
  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return <ThumbsUp className="h-4 w-4 text-green-500" />
      case 'negative':
        return <ThumbsDown className="h-4 w-4 text-red-500" />
      default:
        return <MessageSquare className="h-4 w-4 text-gray-500" />
    }
  }
  
  const getSentimentBadge = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return <Badge className="bg-green-100 text-green-800">إيجابي</Badge>
      case 'negative':
        return <Badge className="bg-red-100 text-red-800">يحتاج إلى تحسين</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">محايد</Badge>
    }
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <MessageSquare className="ml-2 h-5 w-5" />
            ملخصات تعليقات الموجهين
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="bg-red-50 text-red-500 p-4 rounded-md">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center" dir="rtl">
          <MessageSquare className="ml-2 h-5 w-5" />
          ملخصات تعليقات الموجهين
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        {isLoading ? (
          <div className="space-y-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="border-b pb-4 last:border-0">
                <div className="flex justify-between mb-2">
                  <Skeleton className="h-4 w-40" />
                  <Skeleton className="h-6 w-24 rounded-full" />
                </div>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-2" />
                <div className="flex justify-between mt-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
            ))}
          </div>
        ) : mentorFeedback.length > 0 ? (
          <div className="space-y-6">
            {mentorFeedback.map((feedback) => (
              <div key={feedback.id} className="border-b pb-4 last:border-0">
                <div className="flex justify-between mb-2">
                  <h4 className="font-medium">{feedback.mentorName}</h4>
                  {getSentimentBadge(feedback.sentiment)}
                </div>
                <p className="text-sm mb-2">
                  <span className="font-medium">بخصوص: {feedback.articleTitle}</span> - المعلم: {feedback.teacherName}
                </p>
                <p className="text-sm text-gray-700">{feedback.summary}</p>
                <div className="flex justify-between mt-2 text-xs text-gray-500">
                  <span>{formatDistanceToNow(new Date(feedback.createdAt), { addSuffix: true, locale: ar })}</span>
                  <div className="flex items-center">
                    {getSentimentIcon(feedback.sentiment)}
                    <span className="mr-1">
                      {feedback.sentiment === 'positive' ? 'تعليق إيجابي' : 
                       feedback.sentiment === 'negative' ? 'يحتاج إلى تحسين' : 'تعليق محايد'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
            <p className="text-gray-500">لا توجد ملخصات تعليقات للموجهين متاحة</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
