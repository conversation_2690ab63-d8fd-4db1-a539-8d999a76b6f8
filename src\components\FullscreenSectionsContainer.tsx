'use client'

import React from 'react'
import { FullscreenSection } from './FullscreenSection'
import { InteractiveDots } from './InteractiveDots'

// Define a common background color for all sections and footer
const commonBgColor = 'hsl(var(--primary))' // cyan-900

export const FullscreenSectionsContainer: React.FC = () => {
  return (
    <div className="scroll-container overflow-hidden" style={{ position: 'relative' }}>
      <FullscreenSection
        title="For Students"
        description="Submit your articles and get valuable feedback from teachers. Develop your writing skills and share your voice with the world."
        buttonText="Start Writing"
        buttonLink="/articles/create"
        modelType="book"
        color="black" // cyan-500
        bgColor={commonBgColor} // Using common background color
        textColor="#f0f9ff" // cyan-50
        isReversed={false}
        isFirst={true} // This is the first section, so it will have a gradient
      />

      <FullscreenSection
        title="For Teachers"
        description="Review student articles and provide valuable feedback. Help shape the next generation of journalists and writers."
        buttonText="Review Articles"
        buttonLink="/articles/review"
        modelType="users"
        color="black" // violet-500
        bgColor={commonBgColor} // Using common background color
        textColor="#f5f3ff" // violet-50
        isReversed={true}
      />

      <FullscreenSection
        title="Leaderboards"
        description="See top performing schools, teachers, and students. Celebrate excellence and inspire others to achieve greatness."
        buttonText="View Statistics"
        buttonLink="/statistics"
        modelType="award"
        color="black" // amber-500
        bgColor={commonBgColor} // Using common background color
        textColor="#fffbeb" // amber-50
        isReversed={false}
      />

   
    </div>
  )
}
