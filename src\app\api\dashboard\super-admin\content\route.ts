import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import { safePayloadResponse, isMediaPath } from '@/lib/media-utils'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
      const userId = typeof decoded === 'object' ? decoded.id : null
      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const type = url.searchParams.get('type') || 'article'
      const schoolId = url.searchParams.get('schoolId')
      const limit = parseInt(url.searchParams.get('limit') || '50')
      const page = parseInt(url.searchParams.get('page') || '1')

      // Get the payload instance
      const payload = await getPayload({ config })
      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })
      // Check if user is a super-admin
      const role = typeof user.role === 'object' ? user.role?.slug : user.role
      if (role !== 'super-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()
        let content: any[] = []
        const query: any = {}
        // Helper to safely try ObjectId conversion
        function safeObjectId(val: string) {
          try {
            return new ObjectId(val)
          } catch {
            return null
          }
        }
        if (schoolId) {
          if (type === 'article' || type === 'news') {
            const schoolObjId = safeObjectId(schoolId)
            query.$or = [
              { 'author.school.id': schoolId },
              { 'author.school': schoolId },
              ...(schoolObjId ? [{ 'author.school': schoolObjId }] : []),
              { school: schoolId },
              ...(schoolObjId ? [{ school: schoolObjId }] : []),
              { schoolId: schoolId },
              ...(schoolObjId ? [{ schoolId: schoolObjId }] : []),
            ]
          } else if (type === 'comment') {
            const schoolObjId = safeObjectId(schoolId)
            query.$or = [
              { 'school.id': schoolId },
              { school: schoolId },
              ...(schoolObjId ? [{ school: schoolObjId }] : []),
              { schoolId: schoolId },
              ...(schoolObjId ? [{ schoolId: schoolObjId }] : []),
            ]
          }
        }
        if (type === 'article') {
          content = await db
            .collection('articles')
            .find(query)
            .skip((page - 1) * limit)
            .limit(limit)
            .toArray()
          content = content.map((item: any) => {
            if (
              item.featuredImage &&
              typeof item.featuredImage === 'string' &&
              isMediaPath(item.featuredImage)
            ) {
              return { ...item, featuredImage: null }
            }
            return item
          })
        } else if (type === 'news') {
          content = await db
            .collection('news')
            .find(query)
            .skip((page - 1) * limit)
            .limit(limit)
            .toArray()
          content = content.map((item: any) => {
            if (
              item.featuredImage &&
              typeof item.featuredImage === 'string' &&
              isMediaPath(item.featuredImage)
            ) {
              return { ...item, featuredImage: null }
            }
            return item
          })
        } else if (type === 'comment') {
          content = await db
            .collection('systemicIssues')
            .find(query)
            .skip((page - 1) * limit)
            .limit(limit)
            .toArray()
        } else if (type === 'user') {
          if (schoolId) query.$or = [{ 'school.id': schoolId }, { school: schoolId }]
          content = await db
            .collection('users')
            .find(query)
            .skip((page - 1) * limit)
            .limit(limit)
            .toArray()
          content = content.map((item) => {
            if (
              item.profileImage &&
              typeof item.profileImage === 'string' &&
              isMediaPath(item.profileImage)
            ) {
              return { ...item, profileImage: null }
            }
            if (
              item.pendingProfileImage &&
              typeof item.pendingProfileImage === 'string' &&
              isMediaPath(item.pendingProfileImage)
            ) {
              return { ...item, pendingProfileImage: null }
            }
            return item
          })
        }
        if (content.length > 0) {
          const safeContent = safePayloadResponse({ docs: content })
          return NextResponse.json({
            content: safeContent.docs.map((item: any) => {
              if (type === 'comment') {
                return {
                  id: item._id?.toString() || item.id,
                  title: item.title || '',
                  author: item.reporter
                    ? {
                        id: item.reporter?.id || '',
                        name: `${item.reporter?.firstName || ''} ${item.reporter?.lastName || ''}`.trim(),
                      }
                    : undefined,
                  status: item.status || '',
                  schoolName: typeof item.school === 'object' ? item.school?.name : '',
                  schoolId: typeof item.school === 'object' ? item.school?.id : item.school,
                  createdAt: item.createdAt || '',
                  updatedAt: item.updatedAt || '',
                  type,
                }
              }
              // Fix for articles/news: handle author as ObjectId or object
              let author = undefined
              if (item.author) {
                if (typeof item.author === 'object' && (item.author.id || item.author._id)) {
                  author = {
                    id: item.author.id || item.author._id || '',
                    name: item.author.name || '',
                  }
                } else if (typeof item.author === 'object' && item.author.$oid) {
                  author = { id: item.author.$oid, name: '' }
                } else if (typeof item.author === 'string') {
                  author = { id: item.author, name: '' }
                }
              }
              // Handle featuredImage: if not a valid ObjectId, just return as string or null
              let featuredImage = null
              if (item.featuredImage) {
                if (typeof item.featuredImage === 'string') {
                  featuredImage = item.featuredImage
                } else if (typeof item.featuredImage === 'object' && item.featuredImage.$oid) {
                  featuredImage = item.featuredImage.$oid
                } else {
                  try {
                    featuredImage = item.featuredImage.toString()
                  } catch {
                    featuredImage = null
                  }
                }
              }
              console.log(featuredImage)
              return {
                id: item._id?.toString() || item.id,
                title: item.title || item.name || '',
                author,
                firstName: item.firstName || '',
                lastName: item.lastName || '',
                email: item.email || '',
                status: item.status || '',
                schoolName:
                  item.schoolName || (typeof item.school === 'object' ? item.school?.name : ''),
                schoolId:
                  item.schoolId ||
                  (typeof item.school === 'object' ? item.school?.id : item.school),
                createdAt: item.createdAt || '',
                updatedAt: item.updatedAt || '',
                featuredImage,
                type,
              }
            }),
          })
        }
      } catch (mongoError) {
        console.warn('Error fetching content from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails or is empty
      try {
        const where: any = {}
        if (type === 'article' || type === 'news') {
          if (schoolId) where['author.school'] = { equals: schoolId }
          const result = await payload.find({
            collection: type === 'article' ? 'articles' : 'news',
            where,
            limit,
            page,
            depth: 2,
            select: {
              id: true,
              title: true,
              author: true,
              status: true,
              createdAt: true,
              updatedAt: true,
              // featuredImage: false // explicit exclusion
            }
          })
          if (result.docs.length > 0) {
            return NextResponse.json({
              content: result.docs.map((item: any) => ({
                id: item.id,
                title: item.title || '',
                author: item.author
                  ? { id: item.author?.id || '', name: item.author?.name || '' }
                  : undefined,
                status: item.status || '',
                createdAt: item.createdAt || '',
                updatedAt: item.updatedAt || '',
                type,
              })),
            })
          }
        } else if (type === 'comment') {
          if (schoolId) where['school'] = { equals: schoolId }
          const result = await payload.find({
            collection: 'systemicIssues',
            where,
            limit,
            page,
            depth: 2,
          })
          if (result.docs.length > 0) {
            return NextResponse.json({
              content: result.docs.map((item: any) => ({
                id: item.id,
                title: item.title || '',
                author: item.reporter
                  ? {
                      id: item.reporter?.id || '',
                      name: `${item.reporter?.firstName || ''} ${item.reporter?.lastName || ''}`.trim(),
                    }
                  : undefined,
                status: item.status || '',
                schoolName: typeof item.school === 'object' ? item.school?.name : '',
                schoolId: typeof item.school === 'object' ? item.school?.id : item.school,
                createdAt: item.createdAt || '',
                updatedAt: item.updatedAt || '',
                type,
              })),
            })
          }
        } else if (type === 'user') {
          if (schoolId) where['school'] = { equals: schoolId }
          const result = await payload.find({
            collection: 'users',
            where,
            limit,
            page,
            depth: 2,
          })
          if (result.docs.length > 0) {
            const safeResult = safePayloadResponse(result)
            return NextResponse.json({
              content: safeResult.docs.map((item: any) => ({
                id: item.id,
                title: item.name || '',
                firstName: item.firstName || '',
                lastName: item.lastName || '',
                email: item.email || '',
                status: item.status || '',
                schoolName: typeof item.school === 'object' ? item.school?.name : '',
                schoolId: typeof item.school === 'object' ? item.school?.id : item.school,
                createdAt: item.createdAt || '',
                updatedAt: item.updatedAt || '',
                type,
              })),
            })
          }
        } else {
          // Defensive: unknown type
          console.warn('Unknown type in /api/dashboard/super-admin/content:', type)
          return NextResponse.json({ content: [] })
        }
      } catch (payloadError) {
        console.warn('Error fetching content from Payload CMS:', payloadError)
      }

      // If no data, return empty array
      return NextResponse.json({ content: [] })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching content:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
