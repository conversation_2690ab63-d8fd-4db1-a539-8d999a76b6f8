"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/tabs';
import { ContentManager } from '../components/content-manager';
import { FeedbackManager } from '../components/feedback-manager';
import { Skeleton } from '@/components/ui/skeleton';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useEffect, useState } from 'react';

// User type for users array
interface User {
  id: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  email: string;
  role: string | { slug: string };
  status: string;
  grade?: string;
  createdAt?: string;
}

interface School {
  id: string;
  name: string;
  address?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Fetch roles and provide lookup to UsersManager
function useRolesLookup() {
  const [roles, setRoles] = useState<{ [id: string]: string }>({});
  useEffect(() => {
    fetch('/api/dashboard/super-admin/roles')
      .then(res => res.json())
      .then(data => {
        if (data.roles) {
          const lookup: { [id: string]: string } = {};
          data.roles.forEach((role: any) => {
            if (role.id) lookup[role.id] = role.slug;
            if (role._id) lookup[role._id] = role.slug;
            if (role.slug) lookup[role.slug] = role.slug;
          });
          setRoles(lookup);
        }
      });
  }, []);
  return roles;
}

function UsersManager({ schoolId, rolesLookup }: { schoolId: string, rolesLookup: { [id: string]: string } }) {
  const [loading, setLoading] = useState<boolean>(true);
  const [users, setUsers] = useState<User[]>([]);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const res = await fetch(`/api/dashboard/super-admin/users?schoolId=${schoolId}`);
        if (!res.ok) throw new Error('Failed to fetch users');
        const data = await res.json();
        setUsers(data.users || []);
        setLoading(false);
      } catch (e) {
        setError('Failed to load users');
        setLoading(false);
      }
    };
    fetchUsers();
  }, [schoolId]);
  if (loading) return <Skeleton className="h-[300px] w-full" />;
  if (error) return <div className="text-red-500">{error}</div>;

  // Helper to display role
  const getRoleDisplay = (user: any) => {
    if (user.role && typeof user.role === 'object') {
      return user.role.slug || user.role.id || 'Unknown Role';
    }
    if (typeof user.role === 'string') {
      if (rolesLookup[user.role]) return rolesLookup[user.role];
      // If it's an ObjectId string, show as 'Unknown Role'
      if (/^[a-f\d]{24}$/i.test(user.role)) return rolesLookup[user.role] || 'Unknown Role';
      return user.role;
    }
    return 'Unknown Role';
  };

  // Separate students from other users
  const students = users.filter(
    (user) => getRoleDisplay(user) === 'student'
  );
  const others = users.filter(
    (user) => getRoleDisplay(user) !== 'student'
  );

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Students</CardTitle>
        </CardHeader>
        <CardContent>
          <table className="w-full text-sm">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Grade</th>
                <th>Status</th>
                <th>Created</th>
                <th>Role</th>
              </tr>
            </thead>
            <tbody>
              {students.length > 0 ? students.map((user) => (
                <tr key={user.id} className="border-b">
                  <td>{user.name || `${user.firstName || ''} ${user.lastName || ''}`}</td>
                  <td>{user.email}</td>
                  <td>{user.grade || '-'}</td>
                  <td><Badge>{user.status}</Badge></td>
                  <td>{user.createdAt ? new Date(user.createdAt).toLocaleDateString() : '-'}</td>
                  <td>{getRoleDisplay(user)}</td>
                </tr>
              )) : (
                <tr><td colSpan={6} className="text-center text-muted-foreground">No students found</td></tr>
              )}
            </tbody>
          </table>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Other Users</CardTitle>
        </CardHeader>
        <CardContent>
          <table className="w-full text-sm">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Status</th>
                <th>Created</th>
              </tr>
            </thead>
            <tbody>
              {others.length > 0 ? others.map((user) => (
                <tr key={user.id} className="border-b">
                  <td>{user.name || `${user.firstName || ''} ${user.lastName || ''}`}</td>
                  <td>{user.email}</td>
                  <td>{getRoleDisplay(user)}</td>
                  <td><Badge>{user.status}</Badge></td>
                  <td>{user.createdAt ? new Date(user.createdAt).toLocaleDateString() : '-'}</td>
                </tr>
              )) : (
                <tr><td colSpan={5} className="text-center text-muted-foreground">No other users found</td></tr>
              )}
            </tbody>
          </table>
        </CardContent>
      </Card>
    </div>
  );
}

function ActivitiesManager({ schoolId }: { schoolId: string }) {
  const [loading, setLoading] = useState(true);
  const [activities, setActivities] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  useEffect(() => {
    setLoading(true);
    const url = showDetails
      ? `/api/dashboard/super-admin/activities?schoolId=${schoolId}&raw=true`
      : `/api/dashboard/super-admin/activities?schoolId=${schoolId}`;
    fetch(url)
      .then(res => res.json())
      .then(res => { setActivities(res.activities || []); setLoading(false); })
      .catch(e => { setError('Failed to load activities'); setLoading(false); });
  }, [schoolId, showDetails]);
  if (loading) return <Skeleton className="h-[300px] w-full" />;
  if (error) return <div className="text-red-500">{error}</div>;
  // Helper to format details as a pretty list
  function renderDetails(details: any) {
    if (!details || typeof details !== 'object') return '-';
    return (
      <ul className="text-xs text-muted-foreground space-y-1">
        {Object.entries(details).map(([key, value]) => (
          <li key={key}><span className="font-medium text-foreground">{key}:</span> {typeof value === 'string' ? value : JSON.stringify(value)}</li>
        ))}
      </ul>
    );
  }
  function formatDate(date: string) {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleString();
  }
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Activities Log</CardTitle>
        <button
          className="text-xs underline text-blue-600 hover:text-blue-800"
          onClick={() => setShowDetails((v) => !v)}
        >
          {showDetails ? 'Show Summary' : 'Show Details'}
        </button>
      </CardHeader>
      <CardContent>
        {showDetails ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {activities.length > 0 ? activities.map((a, idx) => (
              <div key={a._id?.toString?.() || a._id || idx} className="border rounded-lg p-4 bg-muted/50">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs text-muted-foreground">{formatDate(a.date || a.createdAt)}</span>
                  <span className="text-xs px-2 py-1 rounded bg-gray-200 text-gray-700">{a.activityType || '-'}</span>
                </div>
                <div className="font-medium text-sm mb-1">{a.userName || a.userId || '-'} <span className="text-xs text-muted-foreground">({a.userRole || '-'})</span></div>
                <div className="text-xs mb-2 text-muted-foreground">{a.description || '-'}</div>
                <div className="text-xs font-semibold mb-1">Details:</div>
                {renderDetails(a.details)}
              </div>
            )) : (
              <div className="col-span-2 text-center text-muted-foreground">No activities found</div>
            )}
          </div>
        ) : (
          <table className="w-full text-sm">
            <thead>
              <tr>
                <th>Date</th>
                <th>Count</th>
                <th>School</th>
              </tr>
            </thead>
            <tbody>
              {activities.length > 0 ? activities.map((activity, idx) => (
                <tr key={idx} className="border-b">
                  <td>{activity.date}</td>
                  <td>{activity.count}</td>
                  <td>{activity.schoolName || '-'}</td>
                </tr>
              )) : (
                <tr><td colSpan={3} className="text-center text-muted-foreground">No activities found</td></tr>
              )}
            </tbody>
          </table>
        )}
      </CardContent>
    </Card>
  );
}

// For debugging: Add debug output for ContentManager tabs
// (You can remove this after confirming data loads)
function DebugContent({ schoolId, type }: { schoolId: string; type: string }) {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    setLoading(true);
    fetch(`/api/dashboard/super-admin/content?schoolId=${schoolId}&type=${type}`)
      .then(res => res.json())
      .then(res => { setData(res.content || []); setLoading(false); })
      .catch(e => { setError('Failed to load'); setLoading(false); });
  }, [schoolId, type]);
  if (loading) return <div>Loading {type}...</div>;
  if (error) return <div>Error: {error}</div>;
  return <pre style={{ maxHeight: 200, overflow: 'auto', background: '#eee', fontSize: 12 }}>{JSON.stringify(data, null, 2)}</pre>;
}

export default function SchoolTabs({ schoolId, school }: { schoolId: string; school: School }) {
  const rolesLookup = useRolesLookup();
  return (
    <Tabs defaultValue="overview" className="w-full">
      <TabsList>
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="users">Users</TabsTrigger>
        <TabsTrigger value="article">Articles</TabsTrigger>
        <TabsTrigger value="news">News</TabsTrigger>
        <TabsTrigger value="comment">Comments</TabsTrigger>
        <TabsTrigger value="feedback">Feedback</TabsTrigger>
        <TabsTrigger value="activities">Activities</TabsTrigger>
      </TabsList>
      <TabsContent value="overview">
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>School Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div><strong>Address:</strong> {school.address}</div>
            <div><strong>Created At:</strong> {school.createdAt ? new Date(school.createdAt).toLocaleString() : '-'}</div>
            <div><strong>Updated At:</strong> {school.updatedAt ? new Date(school.updatedAt).toLocaleString() : '-'}</div>
            {/* Add stats and recent activity here if desired */}
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="users">
        <UsersManager schoolId={schoolId} rolesLookup={rolesLookup} />
      </TabsContent>
      <TabsContent value="article">
        <ContentManager schoolId={schoolId} type="article" />
        <DebugContent schoolId={schoolId} type="article" />
      </TabsContent>
      <TabsContent value="news">
        <ContentManager schoolId={schoolId} type="news" />
        <DebugContent schoolId={schoolId} type="news" />
      </TabsContent>
      <TabsContent value="comment">
        <ContentManager schoolId={schoolId} type="comment" />
        <DebugContent schoolId={schoolId} type="comment" />
      </TabsContent>
      <TabsContent value="feedback">
        <FeedbackManager schoolId={schoolId} />
      </TabsContent>
      <TabsContent value="activities">
        <ActivitiesManager schoolId={schoolId} />
      </TabsContent>
    </Tabs>
  );
} 