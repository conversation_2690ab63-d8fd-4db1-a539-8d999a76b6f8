'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from '@/components/ui/use-toast'
import { FileIcon, DownloadIcon, CheckCircleIcon } from 'lucide-react'

export function ReportsExporter() {
  const [reportType, setReportType] = useState('user-activity')
  const [exportFormat, setExportFormat] = useState('csv')
  const [timeframe, setTimeframe] = useState('last-30-days')
  const [schoolId, setSchoolId] = useState<string | null>(null)
  const [includeOptions, setIncludeOptions] = useState({
    includeSchoolDetails: true,
    includeUserDetails: true,
    includeContentMetrics: true,
  })
  const [exporting, setExporting] = useState(false)
  const [reportData, setReportData] = useState<any[] | null>(null)
  const [generatedBlob, setGeneratedBlob] = useState<Blob | null>(null)

  const handleExport = async () => {
    setExporting(true)
    setReportData(null)
    setGeneratedBlob(null)
    
    try {
      // Calculate date ranges based on timeframe
      const endDate = new Date().toISOString()
      let startDate: string
      
      switch(timeframe) {
        case 'last-7-days':
          startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
          break
        case 'last-30-days':
          startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
          break
        case 'last-90-days':
          startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()
          break
        case 'year-to-date':
          startDate = new Date(new Date().getFullYear(), 0, 1).toISOString()
          break
        case 'all-time':
          startDate = ''
          break
        default:
          startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      }
      
      // Build the query parameters
      let url = `/api/dashboard/super-admin/reports?reportType=${reportType}&exportFormat=${exportFormat}`
      if (startDate) url += `&startDate=${startDate}`
      url += `&endDate=${endDate}`
      if (schoolId) url += `&schoolId=${schoolId}`
      
      // Include query parameters based on includeOptions
      Object.entries(includeOptions).forEach(([key, value]) => {
        if (value) url += `&${key}=true`
      })
      
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to generate report')
      
      const result = await response.json()
      
      if (result && result.data && result.data.length > 0) {
        setReportData(result.data)
        
        // Convert data to the selected format
        let blob: Blob | null = null
        
        switch(exportFormat) {
          case 'csv':
            blob = generateCSV(result.data)
            break
          case 'json':
            blob = generateJSON(result.data)
            break
          case 'xlsx':
            // For Excel, we'd typically use a library like exceljs or xlsx
            // For now, we'll fall back to CSV
            blob = generateCSV(result.data)
            break
          case 'pdf':
            // For PDF, we'd typically use a library like pdfmake or jspdf
            // For now, we'll fall back to JSON
            blob = generateJSON(result.data)
            break
          default:
            blob = generateJSON(result.data)
        }
        
        setGeneratedBlob(blob)
        
        toast({
          title: 'Report generated',
          description: `Your ${reportType} report is ready for download`,
        })
      } else {
        toast({
          title: 'No data available',
          description: 'No data found for the selected criteria',
        })
      }
    } catch (error) {
      console.error('Error generating report:', error)
      toast({
        title: 'Error generating report',
        description: 'There was a problem creating your report',
        variant: 'destructive',
      })
    } finally {
      setExporting(false)
    }
  }

  const generateCSV = (data: any[]): Blob => {
    // Get all possible headers from all objects
    const headers = new Set<string>()
    data.forEach(item => {
      Object.keys(item).forEach(key => headers.add(key))
    })
    
    // Convert headers to array
    const headerRow = Array.from(headers)
    
    // Create CSV content
    let csvContent = headerRow.join(',') + '\n'
    
    // Add data rows
    data.forEach(item => {
      const row = headerRow.map(header => {
        const value = item[header]
        if (value === null || value === undefined) return ''
        if (typeof value === 'object') return JSON.stringify(value).replace(/,/g, ';').replace(/"/g, '""')
        return String(value).replace(/,/g, ';').replace(/"/g, '""')
      })
      csvContent += row.join(',') + '\n'
    })
    
    return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  }
  
  const generateJSON = (data: any[]): Blob => {
    return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  }

  const downloadReport = () => {
    if (!generatedBlob || !reportData) return
    
    const link = document.createElement('a')
    link.href = URL.createObjectURL(generatedBlob)
    link.setAttribute('download', `${reportType}-report.${exportFormat}`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const getReportTypeDescription = () => {
    switch (reportType) {
      case 'user-activity':
        return 'مقاييس النشاط لجميع المستخدمين عبر جميع المدارس'
      case 'content-analytics':
        return 'تحليلات للمقالات والأخبار والتعليقات'
      case 'school-performance':
        return 'مقاييس الأداء لكل مدرسة'
      case 'user-roles':
        return 'توزيع المستخدمين حسب الدور عبر النظام'
      case 'system-audit':
        return 'سجل تدقيق شامل لأنشطة النظام'
      default:
        return 'اختر نوع التقرير'
    }
  }

  return (
    <div className="space-y-6" dir="rtl">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>إعدادات التقرير</CardTitle>
            <CardDescription>
              إنشاء تقارير شاملة على مستوى النظام
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="report-type">نوع التقرير</Label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger id="report-type">
                  <SelectValue placeholder="اختر نوع التقرير" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user-activity">تقرير نشاط المستخدم</SelectItem>
                  <SelectItem value="content-analytics">تحليلات المحتوى</SelectItem>
                  <SelectItem value="school-performance">أداء المدرسة</SelectItem>
                  <SelectItem value="user-roles">توزيع أدوار المستخدمين</SelectItem>
                  <SelectItem value="system-audit">سجل تدقيق النظام</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">{getReportTypeDescription()}</p>
            </div>

            <div className="space-y-3">
              <Label htmlFor="timeframe">الإطار الزمني</Label>
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger id="timeframe">
                  <SelectValue placeholder="اختر الإطار الزمني" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-7-days">آخر 7 أيام</SelectItem>
                  <SelectItem value="last-30-days">آخر 30 يومًا</SelectItem>
                  <SelectItem value="last-90-days">آخر 90 يومًا</SelectItem>
                  <SelectItem value="year-to-date">منذ بداية العام</SelectItem>
                  <SelectItem value="all-time">كل الوقت</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>تنسيق التصدير</Label>
              <RadioGroup 
                value={exportFormat} 
                onValueChange={setExportFormat} 
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="csv" id="format-csv" />
                  <Label htmlFor="format-csv">CSV</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="json" id="format-json" />
                  <Label htmlFor="format-json">JSON</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="xlsx" id="format-xlsx" />
                  <Label htmlFor="format-xlsx">Excel</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pdf" id="format-pdf" />
                  <Label htmlFor="format-pdf">PDF</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-3">
              <Label>خيارات التضمين</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-school"
                    checked={includeOptions.includeSchoolDetails}
                    onCheckedChange={(checked) => 
                      setIncludeOptions(prev => ({ ...prev, includeSchoolDetails: !!checked }))
                    }
                  />
                  <label 
                    htmlFor="include-school"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    تفاصيل المدرسة
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-user"
                    checked={includeOptions.includeUserDetails}
                    onCheckedChange={(checked) => 
                      setIncludeOptions(prev => ({ ...prev, includeUserDetails: !!checked }))
                    }
                  />
                  <label 
                    htmlFor="include-user"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    تفاصيل المستخدم
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-content"
                    checked={includeOptions.includeContentMetrics}
                    onCheckedChange={(checked) => 
                      setIncludeOptions(prev => ({ ...prev, includeContentMetrics: !!checked }))
                    }
                  />
                  <label 
                    htmlFor="include-content"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    مقاييس المحتوى
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={handleExport}
              disabled={exporting}
              className="w-full"
            >
              {exporting ? (
                <>
                  <span className="animate-spin mr-2">⏳</span> جاري إنشاء التقرير...
                </>
              ) : (
                <>
                  <FileIcon className="h-4 w-4 ml-2" /> إنشاء التقرير
                </>
              )}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>التقارير المنشأة</CardTitle>
            <CardDescription>
              تنزيل التقارير التي تم إنشاؤها مؤخرًا
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[320px] flex flex-col items-center justify-center">
            {reportData ? (
              <div className="text-center">
                <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-1">التقرير جاهز</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  تم إنشاء تقرير {reportType === 'user-activity' ? 'نشاط المستخدم' : 
                              reportType === 'content-analytics' ? 'تحليلات المحتوى' : 
                              reportType === 'school-performance' ? 'أداء المدرسة' : 
                              reportType === 'user-roles' ? 'توزيع أدوار المستخدمين' : 
                              reportType === 'system-audit' ? 'تدقيق النظام' : 
                              reportType} بنجاح
                </p>
                <p className="text-sm text-muted-foreground mb-6">
                  تم تصدير {reportData.length} سجل
                </p>
                <Button onClick={downloadReport} className="w-full">
                  <DownloadIcon className="h-4 w-4 ml-2" /> تنزيل {exportFormat.toUpperCase()}
                </Button>
              </div>
            ) : (
              <div className="text-center text-muted-foreground">
                <FileIcon className="h-16 w-16 mx-auto mb-4 opacity-30" />
                <p>لم يتم إنشاء تقارير بعد</p>
                <p className="text-sm mt-2">قم بتكوين وإنشاء تقرير لرؤية النتائج</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 