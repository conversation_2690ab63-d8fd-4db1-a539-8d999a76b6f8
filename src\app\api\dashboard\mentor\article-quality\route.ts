'use server'

import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import jwt from 'jsonwebtoken'
import { connectToDatabase } from '@/lib/mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const payload = await getPayload({ config })

    // Verify the token
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

    // Get the user ID from the token
    const userId = typeof decoded === 'object' ? decoded.id : null

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 2,
    })

    // Verify user role - handle both string ID and populated object cases
    let roleSlug: string
    if (typeof user.role === 'string') {
      const role = await payload.findByID({
        collection: 'roles',
        id: user.role,
        depth: 0,
      })
      roleSlug = role?.slug || ''
    } else if (user.role && typeof user.role === 'object') {
      roleSlug = user.role.slug || ''
    } else {
      return NextResponse.json({ error: 'Invalid user role configuration' }, { status: 400 })
    }

    // Check allowed roles
    if (!['mentor', 'super-admin', 'school-admin'].includes(roleSlug)) {
      return NextResponse.json(
        { error: 'Forbidden', message: 'Only mentors and admins can access this endpoint' },
        { status: 403 },
      )
    }

    // Get school ID - handle both string ID and populated object cases
    let schoolId: string | undefined
    if (typeof user.school === 'string') {
      schoolId = user.school
    } else if (user.school && typeof user.school === 'object') {
      schoolId = user.school.id
    }

    // Only require school for non-super-admins
    if (roleSlug !== 'super-admin' && !schoolId) {
      return NextResponse.json(
        { error: 'Mentor must be associated with a school' },
        { status: 400 },
      )
    }

    // Get URL parameters
    const url = new URL(req.url)
    const querySchoolId = url.searchParams.get('schoolId')
    
    // Use the query schoolId if provided, otherwise use the user's schoolId
    const targetSchoolId = querySchoolId || schoolId

    // Get all articles from the school
    const articles = await payload.find({
      collection: 'articles',
      where: {
        'author.school': { equals: targetSchoolId },
      },
      sort: 'createdAt',
    })

    // Group articles by month
    const articlesByMonth: Record<string, { 
      totalRating: number, 
      totalApproved: number, 
      totalArticles: number 
    }> = {}

    // Get the last 6 months
    const now = new Date()
    for (let i = 5; i >= 0; i--) {
      const month = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthKey = `${month.getFullYear()}-${String(month.getMonth() + 1).padStart(2, '0')}`
      const monthName = month.toLocaleString('default', { month: 'short', year: '2-digit' })
      
      articlesByMonth[monthKey] = { 
        totalRating: 0, 
        totalApproved: 0, 
        totalArticles: 0,
        monthName
      }
    }

    // Calculate statistics for each article
    articles.docs.forEach((article) => {
      const createdAt = new Date(article.createdAt)
      const monthKey = `${createdAt.getFullYear()}-${String(createdAt.getMonth() + 1).padStart(2, '0')}`
      
      // Skip if not in the last 6 months
      if (!articlesByMonth[monthKey]) return
      
      articlesByMonth[monthKey].totalArticles++
      
      if (article.teacherReview && article.teacherReview.length > 0) {
        let articleRating = 0
        let isApproved = false
        
        article.teacherReview.forEach((review: any) => {
          if (review.rating) {
            articleRating += review.rating
          }
          if (review.approved) {
            isApproved = true
          }
        })
        
        // Average rating for this article
        const avgRating = article.teacherReview.length > 0 
          ? articleRating / article.teacherReview.length 
          : 0
        
        articlesByMonth[monthKey].totalRating += avgRating
        
        if (isApproved) {
          articlesByMonth[monthKey].totalApproved++
        }
      }
    })

    // Calculate average rating and approval rate for each month
    const trends = Object.entries(articlesByMonth).map(([monthKey, data]) => {
      const averageRating = data.totalArticles > 0 ? data.totalRating / data.totalArticles : 0
      const approvalRate = data.totalArticles > 0 ? data.totalApproved / data.totalArticles : 0
      
      return {
        month: data.monthName,
        averageRating,
        approvalRate,
        totalArticles: data.totalArticles,
      }
    })

    return NextResponse.json({ trends })
  } catch (error) {
    console.error('Error fetching article quality trends:', error)
    return NextResponse.json({ error: 'Failed to fetch article quality trends' }, { status: 500 })
  }
}
