'use client'

import { useEffect, useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { getArticleId } from '@/lib/id-utils'
import { driver } from 'driver.js'
import 'driver.js/dist/driver.css'
import {
  FileText,
  PlusCircle,
  Edit,
  Eye,
  Award,
  Star,
  ThumbsUp,
  MessageSquare,
  User,
} from 'lucide-react'

import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { PointsWidget } from '@/components/dashboard/widgets/PointsWidget'

interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  role?:
    | {
        id: string
        name: string
        slug: string
      }
    | string
  school?:
    | {
        id: string
        name: string
      }
    | string
}

interface StudentStats {
  totalArticles: number
  publishedArticles: number
  draftArticles: number
  pendingArticles: number
  points: number
  rank?: number
  totalStudents?: number
}

interface Article {
  id: string
  title: string
  status: 'draft' | 'pending-review' | 'published'
  createdAt: string
  updatedAt: string
  teacherReviews?: Array<{
    rating: number
    comment: string
    createdAt: string
  }>
}

interface StudentDashboardProps {
  user: User | null
}

export default function StudentDashboard({ user }: StudentDashboardProps) {
  const router = useRouter()
  const [stats, setStats] = useState<StudentStats>({
    totalArticles: 0,
    publishedArticles: 0,
    draftArticles: 0,
    pendingArticles: 0,
    points: 0,
  })
  const [recentArticles, setRecentArticles] = useState<Article[]>([])
  const [recentReviews, setRecentReviews] = useState<
    { articleId: string; articleTitle: string; review: any }[]
  >([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [showTour, setShowTour] = useState(false)
  const [tourPreference, setTourPreference] = useState<boolean>(
    localStorage.getItem('dashboard-tour-seen') ? false : true
  )
  
  const newArticleBtnRef = useRef(null)
  const statsCardsRef = useRef(null)
  const recentArticlesRef = useRef(null)
  const recentReviewsRef = useRef(null)
  const progressRef = useRef(null)

  useEffect(() => {
    async function fetchData() {
      try {
        if (!user) return

        // Fetch student dashboard data
        const response = await fetch(`/api/dashboard/student`)

        if (!response.ok) {
          throw new Error('Failed to fetch dashboard data')
        }

        const data = await response.json()

        setStats(data.stats)
        setRecentArticles(data.recentArticles || [])
        setRecentReviews(data.recentReviews || [])

        setIsLoading(false)
        
        // Show tour after data is loaded
        const hasSeenTour = localStorage.getItem('dashboard-tour-seen')
        if (!hasSeenTour) {
          setShowTour(true)
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('Failed to load dashboard data. Please try again.')
        setIsLoading(false)
      }
    }

    fetchData()
  }, [user])
  
  useEffect(() => {
    if (showTour && !isLoading) {
      const driverObj = driver({
        showProgress: true,
        // Apply theme colors
        popoverClass: 'driver-theme-custom',
        nextBtnText: 'التالي',
        prevBtnText: 'السابق',
        doneBtnText: 'تم',
        animate: true, 
        overlayColor: 'rgba(0, 0, 0, 0.6)',
        allowClose: true, // Ensure closing is allowed
        // Use a simpler approach for custom styling
        steps: [
          {
            element: '.dashboard-header',
            popover: {
              title: 'مرحبًا بك في لوحة التحكم!',
              description: 'هذه هي مساحتك الشخصية حيث يمكنك رؤية مقالاتك وتقدمك.',
              side: 'bottom',
              align: 'start'
            }
          },
          {
            element: newArticleBtnRef.current!,
            popover: {
              title: 'كتابة مقال جديد',
              description: 'انقر هنا لبدء كتابة مقال جديد لصحيفة مدرستك!',
              side: 'left',
              align: 'center'
            }
          },
          {
            element: statsCardsRef.current!,
            popover: {
              title: 'إحصائياتك',
              description: 'هنا يمكنك رؤية ملخص لمقالاتك وإنجازاتك.',
              side: 'bottom',
              align: 'center'
            }
          },
          {
            element: '.stat-card-0',
            popover: {
              title: 'إجمالي المقالات',
              description: 'هذا يوضح عدد المقالات التي كتبتها بشكل إجمالي.',
              side: 'bottom',
              align: 'start'
            }
          },
          {
            element: '.stat-card-1',
            popover: {
              title: 'المقالات المنشورة',
              description: 'هذه هي المقالات التي تمت الموافقة عليها ونشرها للجميع للقراءة!',
              side: 'bottom',
              align: 'start'
            }
          },
          {
            element: '.stat-card-2',
            popover: {
              title: 'بانتظار المراجعة',
              description: 'هذه المقالات تنتظر مراجعة المعلم لها.',
              side: 'bottom',
              align: 'start'
            }
          },
          {
            element: '.stat-card-3',
            popover: {
              title: 'النقاط المكتسبة',
              description: 'تكسب نقاطًا عندما يتم نشر مقالاتك وعندما يقرأها الناس!',
              side: 'bottom',
              align: 'start'
            }
          },
          {
            element: progressRef.current!,
            popover: {
              title: 'تقدمك',
              description: 'تتبع مدى اقترابك من تحقيق أهداف النشر والحصول على النقاط!',
              side: 'top',
              align: 'center'
            }
          },
          {
            element: recentArticlesRef.current!,
            popover: {
              title: 'مقالاتك الأخيرة',
              description: 'هنا يمكنك رؤية أحدث مقالاتك وحالتها.',
              side: 'top',
              align: 'center'
            }
          },
          {
            element: recentReviewsRef.current!,
            popover: {
              title: 'تعليقات المعلمين',
              description: 'يعرض هذا القسم التعليقات التي قدمها معلموك على مقالاتك.',
              side: 'top',
              align: 'center'
            }
          }
        ],
        // Remove onDestroyStarted as it might interfere with the default button behavior
        onDestroyed: () => {
          setShowTour(false);
          // Only update preference when tour is completed or closed
          setTourPreference(false);
        },
        // Explicitly handle done button click
        onCloseClick: () => {
          driverObj.destroy();
        },
        // Explicitly handle next button click for the last step
        onNextClick: (element, step, options) => {
          const state = options.state;
          const stepsLength = options.config.steps?.length || 0;
          const isLastStep = state?.activeIndex === stepsLength - 1;
          
          if (isLastStep) {
            driverObj.destroy();
          } else {
            driverObj.moveNext();
          }
        }
      });
      
      // Add a custom style element for our driver theme
      const style = document.createElement('style');
      style.innerHTML = `
        .driver-theme-custom .driver-next-btn {
          background-color: hsl(var(--primary)) !important;
          color: hsl(var(--primary-foreground)) !important;
        }
        .driver-theme-custom .driver-prev-btn {
          background-color: hsl(var(--muted)) !important;
          color: hsl(var(--muted-foreground)) !important;
        }
        .driver-theme-custom .driver-close-btn {
          background-color: hsl(var(--destructive)) !important;
          color: hsl(var(--destructive-foreground)) !important;
        }
        .driver-theme-custom.driver-popover {
          background-color: hsl(var(--card)) !important;
          color: hsl(var(--card-foreground)) !important;
          border: 1px solid hsl(var(--border)) !important;
        }
      `;
      document.head.appendChild(style);
      
      driverObj.drive();
      
      return () => {
        // Clean up
        document.head.removeChild(style);
        // Ensure driver is destroyed when component unmounts
        if (driverObj) {
          driverObj.destroy();
        }
      }
    }
  }, [showTour, isLoading])

  useEffect(() => {
    // Update the preference whenever it changes
    if (tourPreference) {
      localStorage.removeItem('dashboard-tour-seen');
    } else {
      localStorage.setItem('dashboard-tour-seen', 'true');
    }
  }, [tourPreference]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">تم النشر</Badge>
      case 'pending-review':
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            بانتظار المراجعة
          </Badge>
        )
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">مسودة</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const statCards = [
    {
      title: 'إجمالي المقالات',
      value: stats.totalArticles,
      icon: <FileText className="w-6 h-6" />,
      color: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300',
      link: '/dashboard/my-articles',
    },
    {
      title: 'المنشورة',
      value: stats.publishedArticles,
      icon: <ThumbsUp className="w-6 h-6" />,
      color: 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300',
      link: '/dashboard/my-articles?filter=published',
    },
    {
      title: 'بانتظار المراجعة',
      value: stats.pendingArticles,
      icon: <Eye className="w-6 h-6" />,
      color: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-300',
      link: '/dashboard/my-articles?filter=pending-review',
    },
    {
      title: 'النقاط المكتسبة',
      value: stats.points,
      icon: <Award className="w-6 h-6" />,
      color: 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300',
      link: '/dashboard/points',
    },
  ]

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-80 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4">
        {error}
      </div>
    )
  }

  return (
    <>
      <div dir="rtl" className="flex justify-between mb-8 dashboard-header">
        <div>
          <h1 className="text-3xl font-bold dark:text-white">لوحة تحكم الطالب</h1>
          <p className="text-gray-500 dark:text-gray-400">
            مرحبًا بعودتك، {user?.firstName || 'الطالب'}
          </p>
        </div>
        <Button ref={newArticleBtnRef} onClick={() => router.push('/dashboard/my-articles/create')}>
          <PlusCircle className="ml-2 h-4 w-4" />
          مقال جديد
        </Button>
      </div>

      {/* Stats Cards */}
      <div dir="rtl" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8" ref={statsCardsRef}>
        {statCards.map((stat, index) => (
          <Card key={index} className={`stat-card-${index} p-6 hover:shadow-md transition-shadow`}>
            <div className="flex items-center justify-between">
              <div className={`p-3 rounded-lg ${stat.color}`}>{stat.icon}</div>
              <div className="text-right">
                <h3 className="text-2xl font-bold dark:text-white">{stat.value}</h3>
                <p className="text-gray-500 dark:text-gray-400">{stat.title}</p>
              </div>
            </div>
            <div className="mt-4">
              <Button
                variant="ghost"
                className="w-full justify-start text-primary hover:text-primary/80"
                onClick={() => router.push(stat.link)}
              >
                عرض التفاصيل
              </Button>
            </div>
          </Card>
        ))}

        {/* Points Widget */}
        <div className="col-span-1 md:col-span-2 lg:col-span-2">
          <PointsWidget 
            points={stats.points} 
            rank={stats.rank} 
            totalUsers={stats.totalStudents} 
          />
        </div>

        {/* Quick Action Cards */}
        <Card dir="rtl" className="p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="p-3 rounded-lg bg-cyan-100 dark:bg-cyan-900 text-cyan-600 dark:text-cyan-300">
              <MessageSquare className="w-6 h-6" />
            </div>
            <div className="text-right">
              <h3 className="text-lg font-bold dark:text-white">تعليقات المعلم</h3>
              <p className="text-gray-500 dark:text-gray-400">عرض مراجعات المقالات</p>
            </div>
          </div>
          <div className="mt-4">
            <Button
              variant="ghost"
              className="w-full justify-start text-primary hover:text-primary/80"
              onClick={() => router.push('/dashboard/my-feedback')}
            >
              فتح صندوق التعليقات
            </Button>
          </div>
        </Card>

        <Card dir="rtl" className="p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="p-3 rounded-lg bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300">
              <User className="w-6 h-6" />
            </div>
            <div className="text-right">
              <h3 className="text-lg font-bold dark:text-white">إعدادات الملف الشخصي</h3>
              <p className="text-gray-500 dark:text-gray-400">إدارة ملفك الشخصي</p>
            </div>
          </div>
          <div className="mt-4">
            <Button
              variant="ghost"
              className="w-full justify-start text-primary hover:text-primary/80"
              onClick={() => router.push('/dashboard/profile')}
            >
              إدارة الملف الشخصي
            </Button>
          </div>
        </Card>
      </div>

      {/* Progress */}
      <Card dir="rtl" ref={progressRef} className="p-6 mb-8">
        <h2 className="text-xl font-bold mb-4 dark:text-white">تقدمك</h2>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">المقالات المنشورة</span>
              <span className="text-sm font-medium">{stats.publishedArticles} / 10</span>
            </div>
            <Progress value={(stats.publishedArticles / 10) * 100} className="h-2" />
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">النقاط المكتسبة</span>
              <span className="text-sm font-medium">{stats.points} / 500</span>
            </div>
            <Progress value={(stats.points / 500) * 100} className="h-2" />
          </div>
        </div>
      </Card>

      {/* Tables Section */}
      <div dir="rtl" className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Articles */}
        <Card ref={recentArticlesRef} className="p-6 overflow-hidden">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold dark:text-white">مقالاتك الأخيرة</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard/my-articles')}
            >
              عرض الكل
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>العنوان</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>التاريخ</TableHead>
                <TableHead>الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentArticles.length > 0 ? (
                recentArticles.map((article, index) => (
                  <TableRow key={`article-${article.id || index}`}>
                    <TableCell className="font-medium">{article.title}</TableCell>
                    <TableCell>{getStatusBadge(article.status)}</TableCell>
                    <TableCell>{new Date(article.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            const articleId = getArticleId(article)
                            if (articleId) {
                              router.push(`/articles/${articleId}`)
                            } else {
                              console.error('Cannot view: Article has no valid ID', article)
                              alert('لا يمكن العرض: المقالة ليس لديها معرف صالح')
                            }
                          }}
                          title="عرض المقالة"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {article.status === 'draft' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              const articleId = getArticleId(article)
                              if (articleId) {
                                router.push(`/dashboard/my-articles/edit/${articleId}`)
                              } else {
                                console.error('Cannot edit: Article has no valid ID', article)
                                alert('لا يمكن التعديل: المقالة ليس لديها معرف صالح')
                              }
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                    لم يتم العثور على مقالات
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </Card>

        {/* Recent Reviews */}
        <Card dir="rtl" ref={recentReviewsRef} className="p-6 overflow-hidden">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold dark:text-white">مراجعات المعلم الأخيرة</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard/my-feedback')}
            >
              عرض الكل
            </Button>
          </div>

          {recentReviews.length > 0 ? (
            <div className="space-y-4">
              {recentReviews.map((item, index) => (
                <Card key={`review-${item.articleId || index}`} className="p-4">
                  <div className="flex justify-between mb-2">
                    <h3 className="font-medium">{item.articleTitle}</h3>
                    <div className="flex items-center">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star
                          key={`star-${i}`}
                          className={`h-4 w-4 ${
                            item.review &&
                            item.review.rating &&
                            i < Math.round(item.review.rating / 2)
                              ? 'text-yellow-400 fill-yellow-400'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {item.review && item.review.comment
                      ? item.review.comment
                      : 'لم يتم تقديم تعليق'}
                  </p>
                  <div className="text-xs text-gray-500">
                    {item.review && item.review.createdAt
                      ? new Date(item.review.createdAt).toLocaleDateString()
                      : 'التاريخ غير متوفر'}
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              لا توجد مراجعات حتى الآن. قم بإرسال مقالات للحصول على تعليقات من المعلمين.
            </div>
          )}
        </Card>
      </div>

      {/* Show Tour Button */}
      <div className="fixed bottom-4 right-4 z-50 flex flex-col space-y-2">
        <Button 
          onClick={() => setShowTour(true)} 
          className="bg-primary hover:bg-primary/90 text-white"
        >
          عرض دليل لوحة التحكم
        </Button>
        
        {/* Toggle the automatic tour on login */}
        <div className="bg-card border rounded p-2 shadow-sm">
          <div className="flex items-center justify-between">
            <span className="text-sm ml-2">عرض الدليل عند تسجيل الدخول:</span>
            <Button 
              variant={tourPreference ? "default" : "outline"}
              size="sm"
              onClick={() => setTourPreference(!tourPreference)}
            >
              {tourPreference ? "نعم" : "لا"}
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
