import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    let token = cookieStore.get('payload-token')?.value

    // Also check for token in Authorization header as fallback
    if (!token) {
      const authHeader = req.headers.get('authorization')
      const headerToken = authHeader?.split(' ')[1]

      if (!headerToken) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      token = headerToken
    }

    // Get the payload instance
    const payload = await getPayload({ config })

    // Verify the token
    let decoded
    try {
      decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get the user ID from the token
    const userId = typeof decoded === 'object' ? decoded.id : null

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the current user
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 1,
    })

    // Verify user role - handle both string ID and populated object cases
    let roleSlug: string
    if (typeof user.role === 'string') {
      const role = await payload.findByID({
        collection: 'roles',
        id: user.role,
        depth: 0,
      })
      roleSlug = role?.slug || ''
    } else if (user.role && typeof user.role === 'object') {
      roleSlug = user.role.slug || ''
    } else {
      return NextResponse.json({ error: 'Invalid user role configuration' }, { status: 400 })
    }

    // Check allowed roles
    if (!['mentor', 'super-admin', 'school-admin'].includes(roleSlug)) {
      return NextResponse.json(
        { error: 'Forbidden', message: 'Only mentors and admins can access this endpoint' },
        { status: 403 },
      )
    }

    // Get school ID - handle both string ID and populated object cases
    let schoolId: string | undefined
    if (typeof user.school === 'string') {
      schoolId = user.school
    } else if (user.school && typeof user.school === 'object') {
      schoolId = user.school.id
    }

    // Only require school for non-super-admins
    if (roleSlug !== 'super-admin' && !schoolId) {
      return NextResponse.json(
        { error: 'User must be associated with a school' },
        { status: 400 },
      )
    }

    // Get URL parameters
    const url = new URL(req.url)
    const querySchoolId = url.searchParams.get('schoolId')

    // Use the query schoolId if provided, otherwise use the user's schoolId
    const targetSchoolId = querySchoolId || schoolId

    // Get teacher role ID
    const teacherRole = await payload.find({
      collection: 'roles',
      where: {
        slug: { equals: 'teacher' },
      },
    })

    if (!teacherRole || teacherRole.docs.length === 0) {
      return NextResponse.json({ error: 'Teacher role not found' }, { status: 404 })
    }

    const teacherRoleId = teacherRole.docs[0].id

    // Get teachers from the specified school
    const teachersResponse = await payload.find({
      collection: 'users',
      where: {
        role: {
          equals: teacherRoleId,
        },
        school: {
          equals: targetSchoolId,
        },
      },
      depth: 1,
    })

    // Add additional information about each teacher
    const teachersWithDetails = await Promise.all(
      teachersResponse.docs.map(async (teacher) => {
        try {
          // Get count of articles reviewed by this teacher
          const articlesReviewed = await payload.find({
            collection: 'articles',
            where: {
              'teacherReview.reviewer': {
                equals: teacher.id,
              },
            },
            limit: 0, // Only get count
          })

          // Get count of students approved by this teacher
          const studentsApproved = await payload.find({
            collection: 'users',
            where: {
              role: {
                equals: teacherRoleId,
              },
              approvedBy: {
                equals: teacher.id,
              },
            },
            limit: 0, // Only get count
          })

          return {
            ...teacher,
            stats: {
              articlesReviewed: articlesReviewed.totalDocs,
              studentsApproved: studentsApproved.totalDocs,
            },
          }
        } catch (err) {
          console.error(`Error getting stats for teacher ${teacher.id}:`, err)
          return {
            ...teacher,
            stats: {
              articlesReviewed: 0,
              studentsApproved: 0,
            },
          }
        }
      })
    )

    return NextResponse.json({
      teachers: teachersWithDetails,
      totalTeachers: teachersResponse.totalDocs
    })
  } catch (error) {
    console.error('Error fetching teachers:', error)

    // Provide more detailed error messages
    if (error.message && error.message.includes('jwt')) {
      return NextResponse.json({ error: 'Authentication error: Invalid token' }, { status: 401 })
    }

    if (error.message && error.message.includes('Collection not found')) {
      return NextResponse.json({ error: 'Database error: Collection not found' }, { status: 500 })
    }

    return NextResponse.json({
      error: 'Failed to fetch teachers',
      details: error.message
    }, { status: 500 })
  }
}
