'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { CheckCircle, XCircle, Clock, Star, MessageSquare, Eye, Calendar, Search, Filter, X } from 'lucide-react'
import { format, formatDistanceToNow } from 'date-fns'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

interface Review {
  id: string
  articleId: string
  articleTitle: string
  authorAlias: string
  rating: number
  comment: string
  approved: boolean
  reviewDate: string
  articleUrl?: string
}

export default function ReviewHistoryTimeline() {
  const router = useRouter()
  const [reviews, setReviews] = useState<Review[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [approvalFilter, setApprovalFilter] = useState<string | null>(null)
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false)

  const fetchReviewHistory = async () => {
    try {
      setIsLoading(true)
      
      // Build query parameters for filtering
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (startDate) params.append('startDate', startDate)
      if (endDate) params.append('endDate', endDate)
      if (approvalFilter !== null) params.append('approved', approvalFilter)
      
      const queryString = params.toString() ? `?${params.toString()}` : ''
      const response = await fetch(`/api/dashboard/teacher/review-history${queryString}`)

      if (!response.ok) {
        throw new Error('Failed to fetch review history')
      }

      const data = await response.json()

      // Filter out reviews with missing articles if needed
      const validReviews = (data.reviews || []).filter(
        (review: Review) => review.articleTitle && review.articleTitle !== 'Unknown Article',
      )

      // Sort reviews by date (newest first)
      validReviews.sort((a: Review, b: Review) => {
        return new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime()
      })

      setReviews(validReviews)
    } catch (err) {
      console.error('Error fetching review history:', err)
      setError('تعذر تحميل سجل المراجعات')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchReviewHistory()
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchReviewHistory()
  }
  
  const clearFilters = () => {
    setSearchTerm('')
    setStartDate('')
    setEndDate('')
    setApprovalFilter(null)
    // Fetch without filters
    fetchReviewHistory()
  }

  const applyFilters = () => {
    fetchReviewHistory()
    setIsFilterDialogOpen(false)
  }

  const handleViewArticle = async (articleId: string, articleUrl?: string) => {
    if (!articleId || articleId === '') {
      setError(`المقال لم يعد موجودًا أو تم إزالته`)
      setTimeout(() => setError(''), 3000) // Clear error after 3 seconds
      return
    }

    try {
      // Show loading state
      setIsLoading(true)

      // If we have a specific URL for the article, use it
      if (articleUrl) {
        setIsLoading(false)
        router.push(articleUrl)
        return
      }

      // Otherwise, check if the article exists before navigating
      const response = await fetch(`/api/articles/${articleId}`)

      setIsLoading(false)

      if (response.ok) {
        router.push(`/dashboard/articles/${articleId}`)
      } else {
        // Try alternative URL format
        router.push(`/dashboard/teacher/review/${articleId}`)
      }
    } catch (err) {
      setIsLoading(false)
      console.error('Error checking article:', err)
      setError('تعذر الوصول إلى المقال')
      setTimeout(() => setError(''), 3000) // Clear error after 3 seconds
    }
  }

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>سجل المراجعات التاريخي</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="mr-4 space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-20 w-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6" dir="rtl">
          <div className="text-center py-8 text-red-500">
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between" dir="rtl">
        <CardTitle>سجل المراجعات التاريخي</CardTitle>
        <div className="flex space-x-2 space-x-reverse">
          {/* Search bar */}
          <form onSubmit={handleSearch} className="relative">
            <Input
              placeholder="البحث في المراجعات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 pr-8"
              dir="rtl"
            />
            <Search className="absolute right-2 top-2.5 h-4 w-4 text-gray-400" />
            <Button 
              type="submit" 
              size="sm" 
              className="absolute left-1 top-1 h-8"
              variant="ghost"
            >
              بحث
            </Button>
          </form>
          
          {/* Filter dialog */}
          <Dialog open={isFilterDialogOpen} onOpenChange={setIsFilterDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Filter className="h-4 w-4 ml-1" />
                <span>تصفية</span>
              </Button>
            </DialogTrigger>
            <DialogContent dir="rtl">
              <DialogHeader>
                <DialogTitle>تصفية المراجعات</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="approval">حالة الموافقة</Label>
                  <Select value={approvalFilter || ''} onValueChange={setApprovalFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="جميع الحالات" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">جميع الحالات</SelectItem>
                      <SelectItem value="true">تمت الموافقة</SelectItem>
                      <SelectItem value="false">مرفوض</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="start-date">تاريخ البدء</Label>
                  <Input
                    type="date"
                    id="start-date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="end-date">تاريخ الإنتهاء</Label>
                  <Input
                    type="date"
                    id="end-date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
                
                <div className="flex justify-between pt-2">
                  <Button variant="outline" size="sm" onClick={clearFilters}>
                    إزالة الكل
                  </Button>
                  <Button size="sm" onClick={applyFilters}>
                    تطبيق التصفية
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          {/* Clear filters button - only shows when filters are active */}
          {(searchTerm || startDate || endDate || approvalFilter) && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={clearFilters}
              className="flex items-center gap-1"
            >
              <X className="h-4 w-4 ml-1" />
              <span>إزالة</span>
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent dir="rtl">
        {reviews.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>لا يوجد سجل مراجعات متاح</p>
          </div>
        ) : (
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute right-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

            {/* Timeline items */}
            <div className="space-y-8 relative">
              {reviews.map((review, index) => (
                <div key={review.id} className="mr-12 relative">
                  {/* Timeline dot */}
                  <div
                    className={`absolute -right-12 mt-1.5 w-8 h-8 rounded-full flex items-center justify-center
                    ${review.approved ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}
                  >
                    {review.approved ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <XCircle className="h-5 w-5" />
                    )}
                  </div>

                  {/* Content */}
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium">{review.articleTitle}</h3>
                      <div className="flex items-center">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.round(review.rating / 2)
                                ? 'text-yellow-400 fill-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>

                    <div className="mb-3">
                      <p className="text-sm text-gray-500">الطالب: {review.authorAlias}</p>
                      <p className="text-sm text-gray-500">
                        تمت المراجعة:{' '}
                        {formatDistanceToNow(new Date(review.reviewDate), { addSuffix: true })}
                      </p>
                    </div>

                    <div className="mb-3 p-3 bg-white rounded border">
                      <div className="flex items-start">
                        <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5 ml-2" />
                        <p className="text-sm">{review.comment}</p>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewArticle(review.articleId, review.articleUrl)}
                      >
                        <Eye className="h-4 w-4 ml-1" />
                        عرض المقال
                      </Button>
                    </div>
                  </div>

                  {/* Date indicator */}
                  <div className="absolute -right-28 mt-1.5 text-xs text-gray-500 w-14 text-left">
                    {format(new Date(review.reviewDate), 'MMM d')}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
