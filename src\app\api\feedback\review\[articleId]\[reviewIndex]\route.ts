import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'

import config from '@/payload.config'

export async function POST(
  req: NextRequest, 
  { params }: { params: { articleId: string; reviewIndex: string } }
) {
  try {
    const { articleId, reviewIndex } = params
    const reviewIdx = parseInt(reviewIndex, 10)
    
    const payload = await getPayload({
      config,
    })

    // Get the current user
    const { user } = await payload.auth({ req })

    if (!user) {
      return NextResponse.json(
        { error: 'You must be logged in to review feedback' },
        { status: 401 }
      )
    }

    // Check if user is a mentor
    const isMentor = typeof user.role === 'object' 
      ? user.role?.slug === 'mentor' 
      : user.role === 'mentor'

    if (!isMentor) {
      return NextResponse.json(
        { error: 'Only mentors can review teacher feedback' },
        { status: 403 }
      )
    }

    // Get the article
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
    })

    // Check if article is published
    if (article.status !== 'published') {
      return NextResponse.json(
        { error: 'Only published articles can have feedback reviewed' },
        { status: 400 }
      )
    }

    // Check if the review index is valid
    if (!article.teacherReview || !article.teacherReview[reviewIdx]) {
      return NextResponse.json(
        { error: 'Invalid review index' },
        { status: 400 }
      )
    }

    const formData = await req.formData()
    const rating = parseInt(formData.get('rating') as string, 10)
    const comment = formData.get('comment') as string

    // Validate inputs
    if (!rating || !comment) {
      return NextResponse.json(
        { error: 'Rating and comment are required' },
        { status: 400 }
      )
    }

    if (rating < 1 || rating > 10) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 10' },
        { status: 400 }
      )
    }

    // Update the teacher review with mentor feedback
    const updatedTeacherReview = [...article.teacherReview]
    updatedTeacherReview[reviewIdx] = {
      ...updatedTeacherReview[reviewIdx],
      mentorFeedback: {
        mentor: user.id,
        rating,
        comment,
        createdAt: new Date().toISOString(),
      },
    }

    // Update the article
    await payload.update({
      collection: 'articles',
      id: articleId,
      data: {
        teacherReview: updatedTeacherReview,
      },
    })

    // Redirect to the mentor dashboard
    return NextResponse.redirect(new URL('/dashboard/mentor', req.url))
  } catch (error) {
    console.error('Error reviewing feedback:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
