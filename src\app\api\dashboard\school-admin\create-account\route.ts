import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { verifyJWT } from '@/lib/auth'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'
import crypto from 'crypto'
import bcrypt from 'bcryptjs'

function normalizeId(id: any): string {
  if (!id) return '';
  if (typeof id === 'string') return id;
  if (typeof id === 'object' && typeof id.toHexString === 'function') return id.toHexString();
  if (typeof id === 'object' && id._id && typeof id._id.toHexString === 'function') return id._id.toHexString();
  return String(id);
}

// Helper to get role ID as string or ObjectId
function getRoleIdString(role: any): string {
  if (role instanceof ObjectId) return role.toHexString();
  if (typeof role === 'string') return role;
  return String(role);
}

export async function POST(req: NextRequest) {
  try {
    // Get the token from cookies
    const token = req.cookies.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token and get the user ID
      const { userId } = await verifyJWT(token)

      if (!userId) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
      }

      // Get request body
      const body = await req.json()
      const { firstName, lastName, email, role, schoolId, password } = body

      // Validate required fields
      if (!firstName || !lastName || !email || !role || !schoolId) {
        return NextResponse.json({ error: 'All fields are required' }, { status: 400 })
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return NextResponse.json({ error: 'Invalid email format' }, { status: 400 })
      }

      // Validate role
      if (!['teacher', 'mentor'].includes(role)) {
        return NextResponse.json({ error: 'Invalid role' }, { status: 400 })
      }

      // Try to use MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the admin user from MongoDB
        const adminUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })

        if (adminUser) {
          // Check if user is a school admin or super admin
          let userRole = null;
          if (adminUser.role && typeof adminUser.role === 'object' && adminUser.role.slug) {
            userRole = adminUser.role.slug;
          } else if (adminUser.role && typeof adminUser.role === 'object' && adminUser.role instanceof ObjectId) {
            // Look up the role document
            const roleDoc = await db.collection('roles').findOne({ _id: adminUser.role });
            userRole = roleDoc?.slug || null;
          } else if (typeof adminUser.role === 'string' && adminUser.role.length === 24) {
            // If it's a string ObjectId
            const roleDoc = await db.collection('roles').findOne({ _id: new ObjectId(adminUser.role) });
            userRole = roleDoc?.slug || null;
          } else {
            userRole = adminUser.role;
          }
          if (userRole !== 'school-admin' && userRole !== 'super-admin') {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
          }

          // Get admin's school ID
          let adminSchoolId = adminUser.school;
          if (adminSchoolId && typeof adminSchoolId === 'object') {
            if (adminSchoolId instanceof ObjectId) {
              // use as is
            } else if ('id' in adminSchoolId) {
              adminSchoolId = adminSchoolId.id;
            }
          }
          const adminSchoolIdStr = normalizeId(adminSchoolId)
          const schoolIdStr = normalizeId(schoolId)
          console.log('[CreateAccount] adminSchoolId:', adminSchoolIdStr, 'schoolId:', schoolIdStr, 'userRole:', userRole)

          // Verify the school ID matches the admin's school (unless super admin)
          if (userRole === 'school-admin' && adminSchoolIdStr !== schoolIdStr) {
            return NextResponse.json(
              { error: 'You can only create accounts for your own school' },
              { status: 403 }
            )
          }

          // Check if email already exists
          const existingUser = await db.collection('users').findOne({ email })
          if (existingUser) {
            return NextResponse.json(
              { error: 'A user with this email already exists' },
              { status: 400 }
            )
          }

          // Always assign the created staff to the admin's school (unless super admin)
          let assignedSchoolId = schoolIdStr
          if (userRole === 'school-admin') {
            assignedSchoolId = adminSchoolIdStr
          }

          // Use provided password or generate a temporary one
          const userPassword = password && password.length >= 8 ? password : crypto.randomBytes(8).toString('hex')
          // Hash the password
          const salt = await bcrypt.genSalt(10);
          const hash = await bcrypt.hash(userPassword, salt);

          // Look up the role document by slug and use its _id
          const roleDoc = await db.collection('roles').findOne({ slug: role });
          if (!roleDoc) {
            return NextResponse.json({ error: 'Role not found' }, { status: 400 });
          }
          const roleId = roleDoc._id;

          // Create the user in MongoDB
          const newUser = {
            firstName,
            lastName,
            email,
            role: roleId,
            school: assignedSchoolId,
            status: 'active',
            createdBy: userId,
            createdAt: new Date().toISOString(),
            salt,
            hash,
          }

          const result = await db.collection('users').insertOne(newUser)

          // Log the account creation activity
          await db.collection('activities').insertOne({
            userId: userId,
            userName: `${adminUser.firstName} ${adminUser.lastName}`,
            userRole: userRole,
            schoolId: schoolId,
            activityType: 'account-creation',
            description: `Created ${role} account for ${firstName} ${lastName}`,
            date: new Date().toISOString(),
            details: {
              newUserId: result.insertedId.toString(),
              newUserEmail: email,
              newUserRole: role,
            },
          })

          // In a real implementation, send an email with the temporary password
          // For now, we'll just return it in the response (not secure for production)
          return NextResponse.json({
            success: true,
            message: `Account created for ${firstName} ${lastName}`,
            userId: result.insertedId.toString(),
            tempPassword: !password ? userPassword : undefined,
          })
        }
      } catch (mongoError) {
        console.warn('Error creating account with MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the admin user
      const adminUser = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 1,
      })

      // Check if user is a school admin or super admin
      let userRole = null;
      if (
        (adminUser.role && typeof adminUser.role === 'object' && adminUser.role instanceof ObjectId) ||
        (typeof adminUser.role === 'string' && adminUser.role.length === 24)
      ) {
        // Look up the role document
        const roleDoc = await payload.findByID({
          collection: 'roles',
          id: getRoleIdString(adminUser.role),
          depth: 1,
        });
        userRole = roleDoc?.slug || null;
      } else {
        userRole = adminUser.role;
      }
      if (userRole !== 'school-admin' && userRole !== 'super-admin') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }

      // Get admin's school ID
      let adminSchoolId = adminUser.school;
      if (adminSchoolId && typeof adminSchoolId === 'object') {
        if (adminSchoolId instanceof ObjectId) {
          // use as is
        } else if ('id' in adminSchoolId) {
          adminSchoolId = adminSchoolId.id;
        }
      }
      const adminSchoolIdStr = normalizeId(adminSchoolId);
      const schoolIdStr = normalizeId(schoolId)
      console.log('[CreateAccount] adminSchoolId:', adminSchoolIdStr, 'schoolId:', schoolIdStr, 'userRole:', userRole)

      // Verify the school ID matches the admin's school (unless super admin)
      if (userRole === 'school-admin' && adminSchoolIdStr !== schoolIdStr) {
        return NextResponse.json(
          { error: 'You can only create accounts for your own school' },
          { status: 403 }
        )
      }

      // Always assign the created staff to the admin's school (unless super admin)
      let assignedSchoolId = schoolIdStr
      if (userRole === 'school-admin') {
        assignedSchoolId = adminSchoolIdStr
      }

      // Use provided password or generate a temporary one
      const userPassword = password && password.length >= 8 ? password : crypto.randomBytes(8).toString('hex')
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hash = await bcrypt.hash(userPassword, salt);

      // Look up the role document by slug and use its id
      const roleDoc = await payload.find({
        collection: 'roles',
        where: { slug: { equals: role } },
        limit: 1,
      });
      if (!roleDoc.docs || roleDoc.docs.length === 0) {
        return NextResponse.json({ error: 'Role not found' }, { status: 400 });
      }
      const roleId = roleDoc.docs[0].id;

      // Create the user in Payload
      const newUser = await payload.create({
        collection: 'users',
        data: {
          firstName,
          lastName,
          email,
          role: roleId,
          school: assignedSchoolId,
          status: 'active',
          salt,
          hash,
        },
      })

      // Log the account creation activity
      await payload.create({
        collection: 'activities',
        data: {
          userId,
          school: schoolId,
          activityType: 'student-approval',
          details: {
            newUserId: newUser.id,
            newUserEmail: email,
            newUserRole: role,
          },
        },
      })

      // In a real implementation, send an email with the temporary password
      // For now, we'll just return it in the response (not secure for production)
      return NextResponse.json({
        success: true,
        message: `Account created for ${firstName} ${lastName}`,
        userId: newUser.id,
        tempPassword: !password ? userPassword : undefined,
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error creating account:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
