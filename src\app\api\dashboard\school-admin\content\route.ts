import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import jwt from 'jsonwebtoken'
import { getPayload } from 'payload'
import config from '@/payload.config'
import { connectToDatabase } from '@/lib/mongodb'
import { ObjectId } from 'mongodb'

export async function GET(req: NextRequest) {
  try {
    // Get the token from cookies
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      // Verify the token
      const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')

      // Get the user ID from the token
      const userId = typeof decoded === 'object' ? decoded.id : null

      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Get URL parameters
      const url = new URL(req.url)
      const schoolId = url.searchParams.get('schoolId')
      const type = url.searchParams.get('type') || 'article'
      const limit = parseInt(url.searchParams.get('limit') || '50')

      if (!schoolId) {
        return NextResponse.json({ error: 'School ID is required' }, { status: 400 })
      }

      if (!['article', 'news', 'comment', 'user'].includes(type)) {
        return NextResponse.json({ error: 'Invalid content type' }, { status: 400 })
      }

      // Try to get data from MongoDB first
      try {
        const { db } = await connectToDatabase()

        // Get the user from MongoDB
        const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })

        if (mongoUser) {
          // Check if user is a school admin
          const userRole = typeof mongoUser.role === 'object' ? mongoUser.role.slug : mongoUser.role
          console.log('[SchoolAdminContent] User role check:', userRole)
          
          if (userRole !== 'school-admin' && userRole !== 'super-admin') {
            console.log('[SchoolAdminContent] Permission denied: User role is not school-admin or super-admin')
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          // Get user's school ID
          const userSchoolId =
            typeof mongoUser.school === 'object' ? mongoUser.school.id : mongoUser.school
          const userSchoolIdStr = userSchoolId ? userSchoolId.toString() : ''
          const schoolIdStr = schoolId ? schoolId.toString() : ''
          console.log('[SchoolAdminContent] userSchoolId:', userSchoolIdStr, 'schoolId:', schoolIdStr, 'userRole:', userRole)

          // Verify the school ID matches the user's school
          if (userSchoolIdStr !== schoolIdStr) {
            return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
          }

          let content: any[] = []

          // Get content based on type
          if (type === 'article') {
            content = await db
              .collection('articles')
              .find({
                $or: [
                  { 'author.school.id': schoolIdStr },
                  { 'author.school': schoolIdStr },
                  { school: schoolIdStr },
                ],
              })
              .limit(limit)
              .toArray()
          } else if (type === 'news') {
            content = await db
              .collection('news')
              .find({
                $or: [
                  { 'author.school.id': schoolIdStr },
                  { 'author.school': schoolIdStr },
                  { school: schoolIdStr },
                ],
              })
              .limit(limit)
              .toArray()
          } else if (type === 'comment') {
            content = await db.collection('systemicIssues').find({ schoolId: schoolIdStr }).limit(limit).toArray()
          } else if (type === 'user') {
            content = await db
              .collection('users')
              .find({
                $or: [
                  { 'school.id': schoolIdStr },
                  { school: schoolIdStr },
                ],
              })
              .limit(limit)
              .toArray()
          }

          return NextResponse.json({
            content: content.map((item) => ({
              id: item._id.toString(),
              title: item.title || '',
              author: item.author,
              firstName: item.firstName || '',
              lastName: item.lastName || '',
              email: item.email || '',
              createdAt: item.createdAt || '',
            })),
          })
        }
      } catch (mongoError) {
        console.warn('Error fetching content from MongoDB, falling back to Payload:', mongoError)
      }

      // Fallback to Payload CMS if MongoDB fails
      const payload = await getPayload({ config })

      // Get the current user
      const user = await payload.findByID({
        collection: 'users',
        id: userId,
        depth: 2,
      })

      // Verify user is a school admin
      const userRole = typeof user.role === 'object' ? user.role?.slug : user.role
      console.log('[SchoolAdminContent] User role check (Payload):', userRole)
      
      if (userRole !== 'school-admin' && userRole !== 'super-admin') {
        console.log('[SchoolAdminContent] Permission denied: User role is not school-admin or super-admin (Payload)')
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Get user's school ID
      const userSchoolId = typeof user.school === 'object' ? user.school?.id : user.school
      const userSchoolIdStr = userSchoolId ? userSchoolId.toString() : ''
      const schoolIdStr = schoolId ? schoolId.toString() : ''
      console.log('[SchoolAdminContent] userSchoolId:', userSchoolIdStr, 'schoolId:', schoolIdStr, 'userRole:', userRole)

      // Verify the school ID matches the user's school
      if (userSchoolIdStr !== schoolIdStr) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      let content: any[] = []

      // Get content based on type
      if (type === 'article') {
        const articles = await payload.find({
          collection: 'articles',
          where: {
            'author.school': { equals: schoolIdStr },
          },
          depth: 1,
          limit,
        })
        content = articles.docs
      } else if (type === 'news') {
        const news = await payload.find({
          collection: 'news',
          where: {
            'author.school': { equals: schoolIdStr },
          },
          depth: 1,
          limit,
        })
        content = news.docs
      } else if (type === 'comment') {
        const comments = await payload.find({
          collection: 'systemicIssues',
          where: {
            schoolId: { equals: schoolIdStr },
          },
          limit,
        })
        content = comments.docs
      } else if (type === 'user') {
        const users = await payload.find({
          collection: 'users',
          where: {
            school: { equals: schoolIdStr },
          },
          limit,
        })
        content = users.docs
      }

      return NextResponse.json({
        content: content.map((item) => ({
          id: item.id,
          title: item.title || '',
          author: item.author,
          firstName: item.firstName || '',
          lastName: item.lastName || '',
          email: item.email || '',
          createdAt: item.createdAt || '',
        })),
      })
    } catch (error) {
      console.error('Token verification error:', error)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error fetching content:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

export async function GET_DEBUG(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    const decoded = jwt.verify(token, process.env.PAYLOAD_SECRET || 'secret')
    const userId = typeof decoded === 'object' ? decoded.id : null
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    const { db } = await connectToDatabase()
    const mongoUser = await db.collection('users').findOne({ _id: new ObjectId(userId) })
    let userRole = null
    let userSchool = null
    if (mongoUser) {
      userRole = typeof mongoUser.role === 'object' ? mongoUser.role.slug : mongoUser.role
      userSchool = typeof mongoUser.school === 'object' ? mongoUser.school.id : mongoUser.school
    }
    return NextResponse.json({ userId, userRole, userSchool })
  } catch (error) {
    return NextResponse.json({ error: (error instanceof Error ? error.message : String(error)) || 'Unknown error' }, { status: 500 })
  }
}
