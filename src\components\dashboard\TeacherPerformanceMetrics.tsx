'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { BarChart, AlertCircle, FileText, Users, Clock } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface TeacherPerformance {
  teacherId: string
  teacherName: string
  approvalRate: number
  reviewsCompleted: number
  averageResponseTime: number
  studentsApproved: number
  articlesReviewed: number
}

interface TeacherPerformanceMetricsProps {
  schoolId: string
}

export default function TeacherPerformanceMetrics({ schoolId }: TeacherPerformanceMetricsProps) {
  const [teacherPerformance, setTeacherPerformance] = useState<TeacherPerformance[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('approval-rates')

  useEffect(() => {
    async function fetchTeacherPerformance() {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/dashboard/school-admin/teacher-performance?schoolId=${schoolId}`)
        
        if (!response.ok) {
          throw new Error('فشل في جلب مقاييس أداء المعلمين')
        }
        
        const data = await response.json()
        setTeacherPerformance(data.teacherPerformance || [])
        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching teacher performance:', err)
        setError('فشل في تحميل مقاييس أداء المعلمين')
        setIsLoading(false)
      }
    }
    
    fetchTeacherPerformance()
  }, [schoolId])
  
  const getApprovalRateColor = (rate: number) => {
    if (rate >= 0.8) {
      return 'bg-green-500'
    } else if (rate >= 0.6) {
      return 'bg-yellow-500'
    } else {
      return 'bg-red-500'
    }
  }
  
  const formatResponseTime = (minutes: number) => {
    if (minutes < 60) {
      return `${Math.round(minutes)} دقيقة`
    } else if (minutes < 1440) { // Less than a day
      return `${Math.round(minutes / 60)} ساعة`
    } else {
      return `${Math.round(minutes / 1440)} يوم`
    }
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center" dir="rtl">
            <BarChart className="ml-2 h-5 w-5" />
            مقاييس أداء المعلمين
          </CardTitle>
        </CardHeader>
        <CardContent dir="rtl">
          <div className="bg-red-50 text-red-500 p-4 rounded-md">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center" dir="rtl">
          <BarChart className="ml-2 h-5 w-5" />
          مقاييس أداء المعلمين
        </CardTitle>
      </CardHeader>
      <CardContent dir="rtl">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="approval-rates">معدلات الموافقة</TabsTrigger>
            <TabsTrigger value="response-times">وقت الاستجابة</TabsTrigger>
            <TabsTrigger value="volume-metrics">مقاييس الحجم</TabsTrigger>
          </TabsList>
          
          <TabsContent value="approval-rates">
            {isLoading ? (
              <div className="space-y-6">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-40" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                    <Skeleton className="h-4 w-full" />
                  </div>
                ))}
              </div>
            ) : teacherPerformance.length > 0 ? (
              <div className="space-y-6">
                {teacherPerformance.map((teacher) => (
                  <div key={teacher.teacherId} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">{teacher.teacherName}</h4>
                      <div className="text-sm font-medium">
                        {Math.round(teacher.approvalRate * 100)}% معدل الموافقة
                      </div>
                    </div>
                    <div className="relative pt-1">
                      <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                        <div
                          style={{ width: `${teacher.approvalRate * 100}%` }}
                          className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${getApprovalRateColor(
                            teacher.approvalRate
                          )}`}
                        ></div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      بناءً على {teacher.reviewsCompleted} مراجعات
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
                <p className="text-gray-500">لا تتوفر بيانات معدل موافقة المعلمين</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="response-times">
            {isLoading ? (
              <div className="space-y-6">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-40" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                    <Skeleton className="h-4 w-full" />
                  </div>
                ))}
              </div>
            ) : teacherPerformance.length > 0 ? (
              <div className="space-y-6">
                {teacherPerformance.map((teacher) => (
                  <div key={teacher.teacherId} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">{teacher.teacherName}</h4>
                      <div className="flex items-center text-sm font-medium">
                        <Clock className="h-4 w-4 ml-1" />
                        {formatResponseTime(teacher.averageResponseTime)}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      متوسط وقت الاستجابة للمراجعات
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
                <p className="text-gray-500">لا تتوفر بيانات وقت استجابة المعلمين</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="volume-metrics">
            {isLoading ? (
              <div className="space-y-6">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-40" />
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                  </div>
                ))}
              </div>
            ) : teacherPerformance.length > 0 ? (
              <div className="space-y-6">
                {teacherPerformance.map((teacher) => (
                  <div key={teacher.teacherId} className="space-y-2">
                    <h4 className="font-medium">{teacher.teacherName}</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 ml-2 text-blue-500" />
                        <div>
                          <div className="font-medium">{teacher.articlesReviewed}</div>
                          <div className="text-xs text-gray-500">المقالات المراجعة</div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Users className="h-5 w-5 ml-2 text-green-500" />
                        <div>
                          <div className="font-medium">{teacher.studentsApproved}</div>
                          <div className="text-xs text-gray-500">الطلاب الموافق عليهم</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
                <p className="text-gray-500">لا تتوفر مقاييس حجم المعلمين</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
