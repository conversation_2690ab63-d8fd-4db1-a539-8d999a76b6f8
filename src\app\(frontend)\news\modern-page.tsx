import { headers as getHeaders } from 'next/headers.js'
import Link from 'next/link'
import { getPayload } from 'payload'
import React from 'react'
import Image from 'next/image'

import config from '@/payload.config'
import { Card } from '@/components/ui/card' // Keep Card as it's used
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Eye } from 'lucide-react' // Removed MessageSquare
import { ClientMainCarousel } from './components/ClientMainCarousel'
import { ClientNewsCarousel } from './components/ClientNewsCarousel'
import { ClientBreakingNews } from './components/ClientBreakingNews'
import { ClientBackToTop } from './components/ClientBackToTop'
import { ClientClearButton } from './components/ClientClearButton'
import { getImageUrl } from '@/utils/imageUtils'

import './styles/modern-news.css'
// {{change 1}} Import the News type from payload-types
import { News } from '@/payload-types'

export default async function ModernNewsPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  // {{change 1}} Await getHeaders() before using it
  const headersList = await getHeaders()
  const payloadConfig = await config
  const payload = await getPayload({ config: payloadConfig })
  const { user } = await payload.auth({ headers: headersList })

  // Get search parameters
  const search = typeof searchParams?.search === 'string' ? searchParams.search : ''
  const sort = typeof searchParams?.sort === 'string' ? searchParams.sort : 'latest'
  const page = typeof searchParams?.page === 'string' ? parseInt(searchParams.page, 10) : 1
  const limit = 12 // Number of news posts per page

  // Build the query
  const query: any = {
    // {{consider-lint}} 'any' usage
    status: {
      equals: 'published',
    },
  }

  // Add search term if provided
  if (search) {
    query.or = [
      {
        title: {
          like: search,
        },
      },
      {
        content: {
          like: search,
        },
      },
      // Remove the tags query as it's causing an error
      // We'll handle tag filtering in a different way
    ]
  }

  // Determine sort order
  let sortField = '-createdAt' // Default: latest first
  if (sort === 'oldest') {
    sortField = 'createdAt' // Oldest first
  } else if (sort === 'a-z') {
    sortField = 'title' // Alphabetical A-Z
  } else if (sort === 'z-a') {
    sortField = '-title' // Alphabetical Z-A
  }

  // Get the base URL from headers
  const host = headersList.get('host') || 'localhost:3000'
  const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http'
  const baseUrl = `${protocol}://${host}`

  // Fetch latest news for the main carousel (newest items)
  const newestNewsResponse = await fetch(`${baseUrl}/api/news?limit=1&sort=-createdAt`, {
    cache: 'no-store',
  })
  const newestNews = await newestNewsResponse.json()

  // Fetch most viewed news for featured section
  const topViewedNewsResponse = await fetch(`${baseUrl}/api/news?limit=4&sort=-views`, {
    cache: 'no-store',
  })
  const topViewedNews = await topViewedNewsResponse.json()

  // Fetch all news for general purposes
  const allNewsResponse = await fetch(
    `${baseUrl}/api/news?limit=20&sort=${sortField}${search ? `&search=${encodeURIComponent(search)}` : ''}`,
    {
      cache: 'no-store',
    },
  )
  const allNews = await allNewsResponse.json()

  // Fetch latest news for pagination using the public API with absolute URL
  const latestNewsResponse = await fetch(
    `${baseUrl}/api/news?page=${page}&limit=${limit}&sort=${sortField}${search ? `&search=${encodeURIComponent(search)}` : ''}`,
    {
      cache: 'no-store',
    },
  )
  const latestNews = await latestNewsResponse.json()

  // Check if we have any news items
  const hasNews = allNews.docs.length > 0

  // Get main carousel news (newest item)
  const mainCarouselNews = hasNews ? newestNews.docs : []

  // Get featured news (most viewed items)
  const featuredNews = hasNews ? topViewedNews.docs : []

  // Get side grid news (top 4 most viewed)
  const sideGridNews = hasNews ? featuredNews.slice(0, 4) : []

  // Get breaking news (first 5 items)
  const breakingNews = hasNews ? allNews.docs.slice(0, 5) : []

  // Get featured carousel news (next 8 items)
  const featuredCarouselNews = hasNews ? allNews.docs.slice(4, 12) : []

  // Get latest news for grid (paginated)
  const latestNewsGrid = latestNews.docs

  // Create some default categories since the News collection doesn't have tags
  const defaultCategories = [
    'Announcements',
    'Events',
    'Achievements',
    'Guidelines',
    'Competitions',
    'Resources',
    'News',
    'Updates',
    'Features',
    'Community',
  ]
  const tags = defaultCategories

  return (
    <div className="bg-muted/30 pt-24 font-sans">
      {/* Main News Slider */}
      <div className="container mx-auto px-4 pt-6">
        {!hasNews ? (
          <div className="bg-card p-8 text-center  shadow mb-8">
            <h3 className="text-xl font-semibold mb-2">لا توجد أخبار متاحة</h3>
            <p className="text-muted-foreground mb-6">
              لا توجد عناصر إخبارية متاحة في هذا الوقت. يرجى المراجعة لاحقاً.
            </p>
            {user &&
              ((typeof user.role === 'object' &&
                (user.role?.slug === 'mentor' || user.role?.slug === 'super-admin')) ||
                (typeof user.role === 'string' &&
                  (user.role === 'mentor' || user.role === 'super-admin'))) && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground mb-2">
                    As a {typeof user.role === 'object' ? user.role?.slug : user.role}, you can
                    create news items.
                  </p>
                  <div className="flex justify-center gap-4">
                    <Link
                      href="/admin/seed"
                      className="text-primary hover:text-primary/80 font-medium"
                    >
                      إنشاء أخبار تجريبية
                    </Link>
                    <Link
                      href="/admin/news/create"
                      className="text-primary hover:text-primary/80 font-medium"
                    >
                      إنشاء خبر
                    </Link>
                  </div>
                </div>
              )}
          </div>
        ) : (
          <div className="flex flex-wrap -mx-2 ">
            {/* Main Carousel Column */}
            <div className="w-full lg:w-7/12 px-2">
              <ClientMainCarousel news={mainCarouselNews} />
            </div>

            {/* Side News Grid */}
            <div className="w-full lg:w-5/12 px-2 mt-4 lg:mt-0">
              <div className="grid grid-cols-2 gap-2">
                {sideGridNews.map((news: News) => {
                  // {{change 2}} Add News type
                  // Get featured image
                  const newsImage =
                    news.featuredImage &&
                    typeof news.featuredImage === 'object' &&
                    'url' in news.featuredImage
                      ? news.featuredImage.url
                      : 'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80'

                  // Format date
                  const publishDate = news.publishedAt
                    ? new Date(news.publishedAt)
                    : new Date(news.createdAt)

                  const formattedDate = publishDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: '2-digit',
                  })

                  // Use a default category since News doesn't have tags
                  const category = 'News'

                  return (
                    <div key={news.id} className="relative h-[250px] overflow-hidden ">
                      <Image
                        src={newsImage!}
                        alt={news.title || 'News image'}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 50vw, 25vw"
                      />
                      <div className="absolute inset-0 overlay flex flex-col justify-end p-4">
                        <div className="mb-2 flex items-center">
                          <span className="bg-primary text-primary-foreground uppercase font-semibold px-2 py-1 mr-2 text-xs ">
                            {category}
                          </span>
                          <span className="text-white text-xs">{formattedDate}</span>
                        </div>
                        <Link
                          href={`/news/${encodeURIComponent(news.slug)}`}
                          className="text-white uppercase font-semibold text-xs lg:text-sm hover:text-primary-foreground/80 transition-colors"
                        >
                          {news.title}
                        </Link>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        )}
      </div>

      {hasNews && (
        <>
          {/* Breaking News */}
          <div className="bg-card py-3 mt-4 border-y">
            <div className="container mx-auto px-4">
              <div className="flex items-center">
                <div className="bg-primary text-primary-foreground px-4 py-2 font-semibold text-sm uppercase w-[170px] text-center ">
                  أخبار عاجلة
                </div>
                <div className="flex-grow ml-3 text-card-foreground overflow-hidden">
                  <ClientBreakingNews news={breakingNews} />
                </div>
              </div>
            </div>
          </div>
          {/* Search Bar */}
          <div className="bg-card shadow-sm py-4 px-4 border-b sticky top-0 z-50">
            <div className="container mx-auto">
              <form
                action="/news"
                method="get"
                className="flex flex-col md:flex-row justify-between items-center gap-4"
              >
                <div className="w-full md:w-1/2">
                  <div className="relative">
                    <Input
                      type="text"
                      name="search"
                      placeholder="البحث في الأخبار..."
                      defaultValue={search}
                      className="w-full pl-10"
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <svg
                        className="w-4 h-4 text-muted-foreground"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 20 20"
                      >
                        <path
                          stroke="currentColor"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="flex gap-4 w-full md:w-auto">
                  <select
                    name="sort"
                    className="p-2 border border-input bg-background"
                    defaultValue={sort}
                  >
                    <option value="latest">ترتيب حسب: الأحدث</option>
                    <option value="oldest">ترتيب حسب: الأقدم</option>
                    <option value="a-z">ترتيب حسب: أ-ي</option>
                    <option value="z-a">ترتيب حسب: ي-أ</option>
                  </select>
                  <div className="flex gap-2">
                    <Button type="submit" className="w-full md:w-auto">
                      بحث
                    </Button>
                    {search && <ClientClearButton className="w-full md:w-auto" />}
                  </div>
                </div>
              </form>
            </div>
          </div>

          {/* Featured News */}
          <div className="container mx-auto px-4 py-8">
            <div className="border-l-4 border-primary pl-4 mb-6">
              <h4 className="text-foreground uppercase font-bold text-lg lg:text-xl">
                الأخبار المميزة
              </h4>
            </div>
            <ClientNewsCarousel news={featuredCarouselNews} />
          </div>
        </>
      )}

      {/* Latest News & Sidebar */}
      <div className="container mx-auto px-4 pb-12">
        <div className="flex flex-wrap -mx-4">
          {/* Main Content */}
          <div className="w-full lg:w-8/12 px-4">
            <div className="border-l-4 border-primary pl-4 mb-6">
              <h4 className="text-foreground uppercase font-bold text-lg lg:text-xl">
                {search ? `نتائج البحث: "${search}"` : 'أحدث الأخبار'}
              </h4>
              {search && (
                <p className="text-muted-foreground mt-2">
                  تم العثور على {latestNews.totalDocs} نتيجة لبحثك
                </p>
              )}
            </div>

            {latestNewsGrid.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {latestNewsGrid.map((news: News) => {
                  // {{change 3}} Add News type
                  // Get featured image
                  const newsImage =
                    news.featuredImage &&
                    typeof news.featuredImage === 'object' &&
                    'url' in news.featuredImage
                      ? news.featuredImage.url
                      : 'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80'

                  // Format date
                  const publishDate = news.publishedAt
                    ? new Date(news.publishedAt)
                    : new Date(news.createdAt)

                  const formattedDate = publishDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: '2-digit',
                  })

                  // Use a default category since News doesn't have tags
                  const category = 'News'

                  // Get author name
                  const authorName =
                    typeof news.author === 'object' && news.author
                      ? news.author.firstName && news.author.lastName
                        ? `${news.author.firstName} ${news.author.lastName}`
                        : news.author.email
                      : 'Unknown Author'

                  // Get author image
                  const authorImage =
                    typeof news.author === 'object' && news.author && news.author.profileImage
                      ? getImageUrl(news.author.profileImage)
                      : 'https://i.pravatar.cc/25'

                  // Extract text from rich text object for snippet
                  let contentSnippet = 'اقرأ المزيد...' // Default snippet
                  if (
                    news.content &&
                    typeof news.content === 'object' &&
                    'root' in news.content &&
                    news.content.root.children &&
                    Array.isArray(news.content.root.children) &&
                    news.content.root.children.length > 0
                  ) {
                    // Attempt to find the first text node in the content
                    const firstParagraph = news.content.root.children.find(
                      (child: any) => child.type === 'paragraph',
                    ) // {{consider-lint}} 'any' usage

                    if (
                      firstParagraph &&
                      firstParagraph.children &&
                      Array.isArray(firstParagraph.children) &&
                      firstParagraph.children.length > 0
                    ) {
                      const firstTextNode = firstParagraph.children.find(
                        (child: any) => 'text' in child && typeof child.text === 'string',
                      ) // {{consider-lint}} 'any' usage

                      if (firstTextNode) {
                        // Extract and truncate the text
                        const text = firstTextNode.text
                        contentSnippet = text.length > 150 ? text.substring(0, 150) + '...' : text // Truncate if longer than 150 chars
                      }
                    }
                  }

                  return (
                    <Card key={news.id} className="overflow-hidden">
                      <div className="h-48 overflow-hidden relative">
                        <Image
                          src={newsImage!}
                          alt={news.title || 'News image'}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>
                      <div className="p-4 border-b">
                        <div className="mb-3 flex items-center">
                          <span className="bg-primary text-primary-foreground uppercase font-semibold px-2 py-1 mr-2 text-xs ">
                            {category}
                          </span>
                          <span className="text-muted-foreground text-xs">{formattedDate}</span>
                        </div>
                        <Link
                          href={`/news/${encodeURIComponent(news.slug)}`}
                          className="text-foreground uppercase font-bold text-base lg:text-lg block mb-2 hover:text-primary/80 transition-colors"
                        >
                          {news.title}
                        </Link>
                        <p className="text-muted-foreground text-sm line-clamp-2">
                          {contentSnippet}
                        </p>
                      </div>
                      <div className="flex justify-between items-center p-4">
                        <div className="flex items-center">
                          <div className="w-6 h-6 rounded-full overflow-hidden relative mr-2">
                            <Image
                              src={authorImage}
                              alt={authorName}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <span className="text-muted-foreground text-xs">{authorName}</span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-muted-foreground text-xs">
                            <Eye className="inline-block w-3 h-3 mr-1" />
                            {news.views || 0}
                          </span>
                        </div>
                      </div>
                    </Card>
                  )
                })}
              </div>
            ) : (
              <div className="bg-card p-8 text-center  shadow">
                <h3 className="text-xl font-semibold mb-2">لم يتم العثور على نتائج</h3>
                <p className="text-muted-foreground mb-6">
                  لم نتمكن من العثور على أي أخبار تطابق معايير البحث الخاصة بك.
                </p>
                <Link href="/news" className="text-primary hover:text-primary/80 font-medium">
                  عرض جميع الأخبار
                </Link>
              </div>
            )}

            {/* Pagination */}
            {latestNews.totalPages > 1 && (
              <div className="mt-12 flex justify-center">
                <div className="flex flex-wrap gap-2 justify-center">
                  {Array.from({ length: latestNews.totalPages }, (_, i) => {
                    // Build the query string with current search parameters
                    const pageNum = i + 1
                    const queryParams = new URLSearchParams()
                    if (search) queryParams.set('search', search)
                    if (sort !== 'latest') queryParams.set('sort', sort)
                    queryParams.set('page', pageNum.toString())

                    return (
                      <Link
                        key={i}
                        href={`/news?${queryParams.toString()}`}
                        className={`px-4 py-2 rounded ${
                          latestNews.page === pageNum
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted text-muted-foreground hover:bg-muted/80'
                        }`}
                      >
                        {pageNum}
                      </Link>
                    )
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="w-full lg:w-4/12 px-4 mt-8 lg:mt-0">
            {/* Make the content of the sidebar sticky */}
            <div className="lg:sticky lg:top-24">
              {/* Social Links */}
              <div className="mb-8">
                <div className="border-l-4 border-primary pl-4 mb-4">
                  <h4 className="text-foreground uppercase font-bold text-lg lg:text-xl">تابعنا</h4>
                </div>
                <Card className="p-3 space-y-3">
                  <a
                    href="#"
                    className="block p-4 text-white bg-[#39569E] hover:bg-[#2d4377] transition-colors text-center "
                  >
                    <i className="fab fa-facebook-f"></i> Facebook
                  </a>
                  <a
                    href="#"
                    className="block p-4 text-white bg-[#1DA1F2] hover:bg-[#0d8fd9] transition-colors text-center "
                  >
                    <i className="fab fa-twitter"></i> Twitter
                  </a>
                  <a
                    href="#"
                    className="block p-4 text-white bg-[#C8359D] hover:bg-[#b02a84] transition-colors text-center "
                  >
                    <i className="fab fa-instagram"></i> Instagram
                  </a>
                  <a
                    href="#"
                    className="block p-4 text-white bg-[#DC472E] hover:bg-[#c33a23] transition-colors text-center "
                  >
                    <i className="fab fa-youtube"></i> YouTube
                  </a>
                </Card>
              </div>
              {/* Tags */}
              <Card className="p-4">
                <div className="border-l-4 border-primary pl-4 mb-4">
                  <h4 className="text-foreground uppercase font-bold text-lg lg:text-xl">
                    العلامات
                  </h4>
                </div>
                <div className="flex flex-wrap -m-1">
                  {tags.map((tag, index) => (
                    <Link
                      key={index}
                      href={`/news?search=${encodeURIComponent(tag)}`}
                      className="m-1 px-3 py-1 bg-muted text-xs hover:bg-primary hover:text-primary-foreground transition-colors "
                    >
                      {tag}
                    </Link>
                  ))}
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Back to Top */}
      <ClientBackToTop />
    </div>
  )
}
