import type { CollectionConfig } from 'payload'
import { statisticsAccess } from '../access'

export const Statistics: CollectionConfig = {
  slug: 'statistics',
  admin: {
    useAsTitle: 'name',
  },
  access: {
    read: statisticsAccess,
    create: statisticsAccess,
    update: statisticsAccess,
    delete: statisticsAccess,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Summary Statistics', value: 'summary' },
        { label: 'School Ranking', value: 'schoolRanking' },
        { label: 'Mentor Ranking', value: 'mentorRanking' },
        { label: 'Teacher Ranking', value: 'teacherRanking' },
        { label: 'Student Ranking', value: 'studentRanking' },
      ],
    },
    {
      name: 'data',
      type: 'json',
      required: true,
      admin: {
        description: 'This field stores the ranking data',
      },
      // Define the expected shape of the data based on the type
      hooks: {
        beforeValidate: [
          ({ value, siblingData }) => {
            // Validate data structure based on type
            if (siblingData.type === 'summary') {
              // Ensure summary data has the expected fields
              const expectedFields = [
                'totalArticles',
                'publishedArticles',
                'totalStudents',
                'totalTeachers',
                'totalMentors',
                'totalSchools',
              ]

              // Create default structure if missing
              if (!value || typeof value !== 'object') {
                return {
                  totalArticles: 0,
                  publishedArticles: 0,
                  totalStudents: 0,
                  totalTeachers: 0,
                  totalMentors: 0,
                  totalSchools: 0,
                }
              }

              // Ensure all expected fields exist
              const result = { ...value }
              expectedFields.forEach((field) => {
                if (typeof result[field] !== 'number') {
                  result[field] = 0
                }
              })

              return result
            }

            // For ranking types, ensure it's an array
            if (
              ['schoolRanking', 'mentorRanking', 'teacherRanking', 'studentRanking'].includes(
                siblingData.type,
              )
            ) {
              return Array.isArray(value) ? value : []
            }

            return value
          },
        ],
      },
    },
    {
      name: 'lastUpdated',
      type: 'date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
  ],
  hooks: {
    beforeChange: [
      // Set lastUpdated to current date
      async ({ data }) => {
        data.lastUpdated = new Date().toISOString()
        return data
      },
    ],
  },
}
