'use client'

import { useState, ReactNode, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  Users,
  FileText,
  Newspaper,
  School,
  Award,
  BarChart,
  Settings,
  Menu,
  X,
  Moon,
  Sun,
  Search,
  CheckCircle,
  MessageSquare,
  Palette,
  Check,
  Circle,
  Flag,
  Download,
} from 'lucide-react'
import { NotificationBell } from '@/components/ui/NotificationBell'
import { PointsProvider } from '@/contexts/PointsContext'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { ThemeProvider, useTheme, ThemeColor } from '@/contexts/ThemeContext'
import ThemeSelector from './ThemeSelector'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Dialog<PERSON>rigger,
} from '@/components/ui/dialog'

type ColorOption = {
  value: ThemeColor
  label: string
  color: string
}

interface DashboardLayoutProps {
  children: ReactNode
}

// Inner layout component that uses the theme context
function DashboardLayoutInner({ children }: DashboardLayoutProps) {
  const { darkMode, themeColor, toggleDarkMode, setThemeColor } = useTheme()
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const pathname = usePathname()
  const [userRole, setUserRole] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)
  const [themeDialogOpen, setThemeDialogOpen] = useState(false)
  const [selectedThemeColor, setSelectedThemeColor] = useState<ThemeColor>(themeColor)

  // Define the available color themes
  const colorOptions: ColorOption[] = [
    { value: 'cyan', label: 'أزرق سماوي', color: 'rgb(14, 165, 175)' },
    { value: 'blue', label: 'أزرق', color: 'rgb(59, 130, 246)' },
    { value: 'purple', label: 'بنفسجي', color: 'rgb(139, 92, 246)' },
    { value: 'green', label: 'أخضر', color: 'rgb(34, 197, 94)' },
    { value: 'amber', label: 'كهرماني', color: 'rgb(245, 158, 11)' },
    { value: 'pink', label: 'وردي', color: 'rgb(236, 72, 153)' },
  ]

  // Apply theme color when selected
  const applyThemeColor = (color: ThemeColor) => {
    setSelectedThemeColor(color)
    setThemeColor(color)
    document.documentElement.setAttribute('data-theme', color)
    setThemeDialogOpen(false)
  }

  useEffect(() => {
    async function fetchUserData() {
      // Only fetch user data if it's not already set
      if (user !== null) {
        console.log('User data already loaded, skipping fetch');
        return;
      }

      try {
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
            Expires: '0',
          },
        })

        console.log('Auth response status:', response.status)

        const data = await response.json()
        console.log('Auth data:', data)

        if (response.ok && data.user) {
          setUser(data.user)

          // Determine user role
          const role = typeof data.user.role === 'object' ? data.user.role?.slug : data.user.role
          console.log('User role from auth data:', role)

          setUserRole(role)
        } else {
          console.log('User not authenticated or error:', data.error)
          // Redirect to login if not authenticated
          window.location.href = '/login'
        }
      } catch (err) {
        console.error('Error fetching user data:', err)
      }
    }

    fetchUserData()
  }, [user]) // Only re-run when user changes, not on every render

  // Update selected theme color when theme color changes
  useEffect(() => {
    setSelectedThemeColor(themeColor)
  }, [themeColor])

  // Define navigation items based on user role
  const getNavItems = () => {
    const baseItems = [
      { icon: <LayoutDashboard className="w-5 h-5" />, text: 'لوحة التحكم', href: '/dashboard' },
    ]

    // Super Admin navigation items
    if (userRole === 'super-admin') {
      return [
        ...baseItems,
        { icon: <Users className="w-5 h-5" />, text: 'المستخدمون', href: '/dashboard/users' },
        { icon: <FileText className="w-5 h-5" />, text: 'المقالات', href: '/dashboard/articles' },
        { icon: <Newspaper className="w-5 h-5" />, text: 'الأخبار', href: '/dashboard/news' },
        { icon: <School className="w-5 h-5" />, text: 'المدارس', href: '/dashboard/schools' },
        { icon: <Award className="w-5 h-5" />, text: 'الإنجازات', href: '/dashboard/achievements' },
        { icon: <BarChart className="w-5 h-5" />, text: 'الإحصائيات', href: '/dashboard/statistics' },
        { icon: <MessageSquare className="w-5 h-5" />, text: 'التعليقات والتقارير', href: '/dashboard/feedback' },
        { icon: <CheckCircle className="w-5 h-5" />, text: 'الأنشطة', href: '/dashboard/activities' },
      ]
    }

    // School Admin navigation items
    if (userRole === 'school-admin') {
      return [
        ...baseItems,
        { icon: <Users className="w-5 h-5" />, text: 'المعلمون', href: '/dashboard/teachers' },
        { icon: <Users className="w-5 h-5" />, text: 'الموجهون', href: '/dashboard/mentors' },
        { icon: <Users className="w-5 h-5" />, text: 'الطلاب', href: '/dashboard/students' },
        { icon: <FileText className="w-5 h-5" />, text: 'المقالات', href: '/dashboard/articles' },
        { icon: <Newspaper className="w-5 h-5" />, text: 'الأخبار', href: '/dashboard/news' },
        {
          icon: <MessageSquare className="w-5 h-5" />,
          text: 'الأنشطة',
          href: '/dashboard/activities',
        },
        {
          icon: <Flag className="w-5 h-5" />,
          text: 'التقارير',
          href: '/dashboard/reports',
        },
        {
          icon: <School className="w-5 h-5" />,
          text: 'الملف الشخصي للمدرسة',
          href: '/dashboard/school-profile',
        },
      ]
    }

    // Teacher navigation items
    if (userRole === 'teacher') {
      return [
        ...baseItems,
        { icon: <Users className="w-5 h-5" />, text: 'طلابي', href: '/dashboard/students' },
        { icon: <FileText className="w-5 h-5" />, text: 'المقالات', href: '/dashboard/articles' },
        {
          icon: <CheckCircle className="w-5 h-5" />,
          text: 'موافقات الطلاب',
          href: '/dashboard/student-approvals',
        },
        { icon: <Newspaper className="w-5 h-5" />, text: 'الأخبار', href: '/dashboard/news' },
      ]
    }

    // Mentor navigation items
    if (userRole === 'mentor') {
      return [
        ...baseItems,
        { icon: <Users className="w-5 h-5" />, text: 'المعلمون', href: '/dashboard/teachers' },
        { icon: <Newspaper className="w-5 h-5" />, text: 'الأخبار', href: '/dashboard/news' },
      ]
    }

    // Student navigation items
    return [
      ...baseItems,
      {
        icon: <FileText className="w-5 h-5" />,
        text: 'مقالاتي',
        href: '/dashboard/my-articles',
      },
      {
        icon: <MessageSquare className="w-5 h-5" />,
        text: 'تعليقاتي',
        href: '/dashboard/my-feedback',
      },
      { icon: <Newspaper className="w-5 h-5" />, text: 'الأخبار', href: '/dashboard/news' },
    ]
  }

  const navItems = getNavItems()

  // Get user ID and role for providers
  const userId = user?.id || ''
  const schoolId = typeof user?.school === 'object' ? user?.school?.id : user?.school || ''

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''}`} dir="rtl">
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <aside
          className={`${sidebarOpen ? 'w-64' : 'w-20'} bg-white dark:bg-gray-800 h-screen transition-all duration-300 ease-in-out border-l border-border`}
        >
          <div className="flex flex-col h-full">
            <div className="flex items-center gap-2 p-4 h-16 border-b border-border">
              <LayoutDashboard className="w-8 h-8 text-primary" />
              {sidebarOpen && (
                <h1 className="text-xl font-bold dark:text-white">الصحفي الصغير</h1>
              )}
            </div>

            <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
              {navItems.map((item, index) => {
                const isActive = pathname === item.href
                return (
                  <Link
                    key={index}
                    href={item.href}
                    className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                      isActive
                        ? 'bg-primary/10 text-primary'
                        : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    {item.icon}
                    {sidebarOpen && <span>{item.text}</span>}
                  </Link>
                )
              })}
            </nav>

            <div className="p-4 border-t border-border space-y-2">
              {/* Dark Mode Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleDarkMode}
                className="w-full flex items-center justify-center p-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              >
                {darkMode ? <Sun className="w-5 h-5 ml-3" /> : <Moon className="w-5 h-5 ml-3" />}
                {sidebarOpen && (
                  <span>{darkMode ? 'الوضع النهاري' : 'الوضع الليلي'}</span>
                )}
              </Button>

              {/* Theme Color Button - Opens theme dialog */}
              {sidebarOpen && (
                <Dialog open={themeDialogOpen} onOpenChange={setThemeDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full flex items-center justify-start p-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                    >
                      <Palette className="w-5 h-5 ml-3" />
                      <span>ألوان السمة</span>
                      <div className="mr-auto flex items-center gap-1">
                        <div className="w-3 h-3 rounded-full bg-cyan-500" />
                        <div className="w-3 h-3 rounded-full bg-blue-500" />
                        <div className="w-3 h-3 rounded-full bg-purple-500" />
                      </div>
                    </Button>
                  </DialogTrigger>
                  <DialogContent
                   className="sm:max-w-md ">
                    <DialogHeader
                    className='text-right'
                    >
                      <DialogTitle>اختر لون السمة</DialogTitle>
                      <DialogDescription>
                        حدد لون السمة للوحة التحكم الخاصة بك
                      </DialogDescription>
                    </DialogHeader>
                    <div className="grid grid-cols-3 gap-4 py-4">
                      {colorOptions.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={`flex flex-col items-center justify-center p-6 gap-2 ${
                            selectedThemeColor === color.value ? 'ring-2 ring-primary ring-offset-2' : ''
                          }`}
                          onClick={() => applyThemeColor(color.value)}
                        >
                          <div 
                            className="w-8 h-8 rounded-full" 
                            style={{ backgroundColor: color.color }}
                          />
                          <span>{color.label}</span>
                          {selectedThemeColor === color.value && (
                            <Check className="absolute top-2 left-2 h-4 w-4 text-primary" />
                          )}
                        </Button>
                      ))}
                    </div>
                    <div className="flex justify-between">
                      <Button variant="outline" onClick={() => setThemeDialogOpen(false)}>
                        إلغاء
                      </Button>
                      <Button onClick={() => applyThemeColor(selectedThemeColor)}>
                        تطبيق السمة
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden bg-gray-50 dark:bg-gray-900">
          {/* Navbar */}
          <header className="bg-white dark:bg-gray-800 border-b border-border h-16">
            <div className="flex items-center justify-between px-4 h-full">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                >
                  {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
                </Button>
                <div className="hidden md:flex relative w-64">
                  <Search className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <form action="/dashboard/search" method="GET">
                    <input
                      type="search"
                      name="q"
                      placeholder="البحث..."
                      className="w-full pr-8 h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    />
                  </form>
                </div>
              </div>

              <div className="flex items-center gap-4">
                {/* Theme Selector in Navbar */}
                <ThemeSelector />
                
                {/* Notification Bell */}
                <NotificationBell />

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="relative">
                      {/* Show profile image if available, otherwise show initials */}
                      {user?.profileImage ? (
                        <div className="w-8 h-8 rounded-full overflow-hidden">
                          <img 
                            src={typeof user.profileImage === 'string' 
                              ? user.profileImage 
                              : typeof user.profileImage === 'object' && user.profileImage?.url 
                                ? user.profileImage.url 
                                : ''}
                            alt="الملف الشخصي" 
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center">
                          <span className="text-sm font-medium">
                            {user?.firstName && user?.lastName
                              ? `${user.firstName[0]}${user.lastName[0]}`
                              : user?.email?.[0]?.toUpperCase() || 'م'}
                          </span>
                        </div>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem className="font-medium">
                      {user?.firstName && user?.lastName
                        ? `${user.firstName} ${user.lastName}`
                        : user?.email || 'المستخدم'}
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Link href="/dashboard/profile" className="w-full">
                        الملف الشخصي
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Link href="/api/auth/logout" className="w-full">
                        تسجيل الخروج
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-y-auto p-6">{children}</main>
        </div>
      </div>
    </div>
  )
}

// Outer layout component that provides the theme context
export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [userId, setUserId] = useState('')
  const [userRole, setUserRole] = useState('')
  const [schoolId, setSchoolId] = useState('')
  
  useEffect(() => {
    async function fetchUserData() {
      try {
        const response = await fetch('/api/auth/me')
        if (response.ok) {
          const data = await response.json()
          if (data.user) {
            setUserId(data.user.id || '')
            
            // Set user role
            const role = typeof data.user.role === 'object' ? data.user.role?.slug : data.user.role
            setUserRole(role || '')
            
            // Set school ID
            const school = typeof data.user.school === 'object' ? data.user.school?.id : data.user.school
            setSchoolId(school || '')
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error)
      }
    }
    
    fetchUserData()
  }, [])
  
  return (
    <NotificationProvider userRole={userRole} userId={userId} schoolId={schoolId}>
      <PointsProvider userRole={userRole} userId={userId}>
        <ThemeProvider userId={userId}>
          <DashboardLayoutInner>{children}</DashboardLayoutInner>
        </ThemeProvider>
      </PointsProvider>
    </NotificationProvider>
  )
}
